# TORUN: `./test.sh -f lib/mongo_db.js`

should = require('should')
# mongo_db = require('../../built/lib/mongo4')
testCol = null
debug = null
helpers = require('../00_common/helpers')
{getIndexes,createIndexes} = require('../../built/libapp/mongoTableHelper')
mongo4 = require('../../built/lib/mongo4')

describe 'Test mongo_db',->
  before (done) ->
    @timeout(300000)
    debug = helpers.DEBUG()
    dbs = [
      { db: 'chome', table: 'test' }
    ]
    testCol = helpers.COLLECTION 'chome','test'
    helpers.cleanDBs dbs, (err) ->
      # helpers.checkAndsetUpFixture {folder:__dirname}, ()->
      debug.error err
      setTimeout(done, 2500)
    return
    # testCol = helpers.COLLECTION('vow','testMtCol')
    # testCol.deleteMany {}
    # done()


  describe 'set _mt for save and insert',->
    tests=[
      # {
      #   method:'insert',
      #   query:{_id:'insert.one'}
      #   obj: {_id:'insert.one'},
      # },
      # {
      #   method:'insert',
      #   query:{_id:{$in:['insert.array1','insert.array2']}}
      #   obj: [{_id:'insert.array1'},{_id:'insert.array2'}],
      # },
      {
        method:'insertOne',
        query:{_id:'insertOne'}
        obj: {_id:'insertOne'},
      },
      {
        method:'insertMany',
        query:{_id:{$in:['insertMany1','insertMany2']}}
        obj: [{_id:'insertMany1'},{_id:'insertMany2'}],
      }
    ]
    tests.forEach (test)->
      it "should add _mt to #{test.method}  #{JSON.stringify(test.obj)}", ()->
        try
          ret = await testCol[test.method] test.obj
          # setTimeout  ()->
          ret = await testCol.findToArray test.query
          debug.debug ret
          # console.log '++++++ret',ret
          ret.length.should.above(0)
          for obj in ret
            should.exists(obj._mt)
        catch err
          debug.error err if err
    

  describe 'set _mt for replace test',->
    tests=[
      #db.getCollection('testMtCol').replaceOne({_id:'replaceOne'},{_id:'replaceOne',desc:'replaceOne with new id'},{upsert:true})
      {
        method:'replaceOne',
        query:{_id:'replaceOne'}
        update: {_id:'replaceOne',desc:'replaceOne with new id'},
        opt:{upsert:true}
      },
      {
        method:'replaceOne',
        query:{_id:'save'}
        update: {_id:'save',desc:'replaceOne with existing id'},
        opt:{upsert:true}
      },
      # {
      #   method:'update',
      #   query: {method:'update'}
      #   update: {$set:{desc:'update'}}
      #   opt:{upsert:true}
      # },
      {
        method:'updateOne',
        query: {method:'update'}
        update: {$set:{desc:'updateOne'}}
        opt:{upsert:true}
        expRet:{desc:'updateOne'}
      },
      {
        method:'updateOne',
        query: {method:'push'}
        update: {$push:{desc:'push update'}}
        opt:{upsert:true}
        expRet:{desc:'updateOne'}
      },
      {
        method:'updateMany',
        query: {method:'update'}
        update: {$set:{desc:'updateMany'}}
        opt: {upsert:true}
        expRet:{desc:'updateMany'}
      },
      {
        method:'updateMany',
        query: {method:'updateMany1'}
        update: [{$set:{desc:'updateMany with update in array'}}]
        opt: {upsert:true}
        expRet:{desc:'updateMany with update in array'}
      },
      {
        method:'updateMany',
        query: {method:'updateMany2'}
        update: [{$unset:'desc'}]
        opt: {upsert:true}
        expRet:{}
      },
      {
        method:'findOneAndUpdate',
        query: {_id:'findOneAndUpdate'}
        update: {$set:{desc:'findOneAndUpdate'}}
        opt:{upsert:true}
      },
      {
        method:'findOneAndReplace',
        query: {_id:'findOneAndReplace'}
        update: {desc:'findOneAndReplace'}
        opt:{upsert:true}
      },
      {
        method:'findOneAndReplace',
        query: {_id:'findOneAndReplace'}
        update: {desc:'findOneAndReplace existing id'}
        opt:{upsert:true}
      }
    ]
    tests.forEach (test)->
      it "should add _mt to #{test.method}  #{JSON.stringify(test.update)}", ()->
        try
          ret = await testCol[test.method] test.query,test.update,test.opt
          ret = await testCol.findToArray test.query
          ret.length.should.above(0)
          for obj in ret
            should.exists(obj._mt)
            if test.update?.$set?.desc
              obj.desc.should.equal(test.update.$set.desc)
        catch err
          console.log err if err
          debug.error err if err

      
  describe 'test noQueryNormalize',->
    it "test noQueryNormalize 1", ()->
      # @timeout(10000)
      try
        ret = await testCol.deleteMany {uid:'5fd7df303287585d84f9769c'},{noQueryNormalize:true}
        
        ret = await testCol.insertOne {uid:'5fd7df303287585d84f9769c'},{noQueryNormalize:true}
        ret = await testCol.findOne {uid:'5fd7df303287585d84f9769c'}
            #query will be normalized, should get ret null
        console.log 'findOne', ret
        should.not.exists(ret)
        ret = await testCol.findOne {uid:'5fd7df303287585d84f9769c'},{noQueryNormalize:true}
        should.exists(ret)
        ret = await testCol.updateOne {uid:'5fd7df303287585d84f9769c'},{$set:{desc:'test'}},{}
        ret = await testCol.findOne {uid:'5fd7df303287585d84f9769c'},{noQueryNormalize:true}
        should.not.exists(ret.desc)
        ret = await testCol.updateOne {uid:'5fd7df303287585d84f9769c'},{$set:{desc:'test'}},{noQueryNormalize:true}
        ret = await testCol.findOne {uid:'5fd7df303287585d84f9769c'},{noQueryNormalize:true}
        ret.desc.should.equal('test')
      catch err
        console.log err
        debug.error err
    
    it "test noQueryNormalize 2", ()->
      # @timeout(10000)
      try
        ret = await testCol.deleteMany {_id:'5fd7df303287585d84f9767c'}
        ret = await testCol.insertOne {_id:'5fd7df303287585d84f9767c'}
        ret = await testCol.findOne {_id:'5fd7df303287585d84f9767c'}
        #query will be normalized, should get ret null
        console.log ret
        should.exists(ret)
        ret = await testCol.findOne {_id:'5fd7df303287585d84f9767c'},{noQueryNormalize:true}
        #query will be normalized, should get ret null
        should.not.exists(ret)
      catch err
        console.log err
        debug.error err
      
  # cleanDbs [ { db: 'chome', table: 'test' } ]
  # MongoError: test.rename MongoServerError: cannot perform operation: an index build is currently running for collection
  describe 'test getIndexes and createIndexes',->
    indexesArr = [
      {
        index: { method: 1 },
        option: { name: 'method_1', background: true }
      }
    ]
    expectedIndexes = [
      { v: 2, key: { _id: 1 }, name: '_id_' }
      { v: 2, key: { method: 1 }, name: 'method_1', background: true }
    ]
    it 'should create method index',(done)->
      @timeout(10000)
      createIndexes testCol,indexesArr,(err,ret)->
        should.not.exists(err)
        getIndexes testCol,(err, indexes)->
          should.not.exists(err)
          should.deepEqual(expectedIndexes,indexes)
          done()
        return
      return

  describe 'test findOneAnd* methods return values',->
    it 'should return old format by default (returnDocument: before)', ()->
      try
        # First insert a test document
        await testCol.insertOne {_id: 'test_update_default', value: 'original', count: 0}
        
        # Test findOneAndUpdate without any options
        ret = await testCol.findOneAndUpdate(
          {_id: 'test_update_default'},
          {$inc: {count: 1}, $set: {value: 'updated'}}
        )
        
        # Verify return structure
        should.exists(ret)
        should.exists(ret.ok)
        should.exists(ret.value)
        should.exists(ret.lastErrorObject)
        ret.value.count.should.equal(0)
        
        # Verify the document was actually updated
        doc = await testCol.findOne {_id: 'test_update_default'}
        doc.value.should.equal('updated')
        doc.count.should.equal(1)
        
        # Clean up
        await testCol.deleteOne {_id: 'test_update_default'}
      catch err
        should.not.exists(err)
        debug.error err

    it 'should return old format when returnDocument is after for findOneAndUpdate', ()->
      try
        # First insert a test document
        await testCol.insertOne {_id: 'test_update_after', value: 'original', count: 0}
        
        # Test findOneAndUpdate with returnDocument: 'after'
        ret = await testCol.findOneAndUpdate(
          {_id: 'test_update_after'},
          {$inc: {count: 1}, $set: {value: 'updated'}},
          {returnDocument: 'after'}
        )
        
        # Verify return structure (should be old format)
        should.exists(ret)
        should.exists(ret.ok)
        should.exists(ret.value)
        should.exists(ret.lastErrorObject)
        # Verify returned document is updated
        ret.value.value.should.equal('updated')
        ret.value.count.should.equal(1)
        
        # Verify the document was actually updated
        doc = await testCol.findOne {_id: 'test_update_after'}
        doc.value.should.equal('updated')
        doc.count.should.equal(1)
        
        # Verify lastErrorObject structure
        ret.lastErrorObject.n.should.equal(1)
        ret.lastErrorObject.updatedExisting.should.be.true()
        
        # Clean up
        await testCol.deleteOne {_id: 'test_update_after'}
      catch err
        should.not.exists(err)
        debug.error err

    it 'should return old format when returnDocument is before for findOneAndUpdate', ()->
      try
        # First insert a test document
        await testCol.insertOne {_id: 'test_update_before', value: 'original', count: 0}
        
        # Test findOneAndUpdate with returnDocument: 'after'
        ret = await testCol.findOneAndUpdate(
          {_id: 'test_update_before'},
          {$inc: {count: 1}, $set: {value: 'updated'}},
          {returnDocument: 'before'}
        )
        
        # Verify return structure (should be old format)
        should.exists(ret)
        should.exists(ret.ok)
        should.exists(ret.value)
        should.exists(ret.lastErrorObject)
        
        # Verify the document was actually updated
        doc = await testCol.findOne {_id: 'test_update_before'}
        doc.value.should.equal('updated')
        doc.count.should.equal(1)
        
        # Verify lastErrorObject structure
        ret.lastErrorObject.n.should.equal(1)
        ret.lastErrorObject.updatedExisting.should.be.true()
        
        # Clean up
        await testCol.deleteOne {_id: 'test_update_before'}
      catch err
        should.not.exists(err)
        debug.error err

    it 'should return old format when returnDocument is after for findOneAndReplace', ()->
      try
        # First insert a test document
        await testCol.insertOne {_id: 'test_replace_after', value: 'original', count: 0}
        
        # Test findOneAndReplace with returnDocument: 'after'
        ret = await testCol.findOneAndReplace(
          {_id: 'test_replace_after'},
          {_id: 'test_replace_after', value: 'replaced', count: 1},
          {returnDocument: 'after'}
        )
        
        # Verify return structure (should be old format)
        should.exists(ret)
        should.exists(ret.ok)
        should.exists(ret.value)
        should.exists(ret.lastErrorObject)
        
        # Clean up
        await testCol.deleteOne {_id: 'test_replace_after'}
      catch err
        should.not.exists(err)
        debug.error err

    it 'should return old format when returnDocument is before for findOneAndReplace', ()->
      try
        # First insert a test document
        await testCol.insertOne {_id: 'test_replace_before', value: 'original', count: 0}
        
        # Test findOneAndReplace with returnDocument: 'after'
        ret = await testCol.findOneAndReplace(
          {_id: 'test_replace_before'},
          {_id: 'test_replace_before', value: 'replaced', count: 1},
          {returnDocument: 'before'}
        )
        
        # Verify return structure (should be old format)
        should.exists(ret)
        should.exists(ret.ok)
        should.exists(ret.value)
        should.exists(ret.lastErrorObject)
        
        ret.value.count.should.equal(0)
        
        # Verify the document was actually replaced
        doc = await testCol.findOne {_id: 'test_replace_before'}
        doc.value.should.equal('replaced')
        doc.count.should.equal(1)
        
        # Verify lastErrorObject structure
        ret.lastErrorObject.n.should.equal(1)
        ret.lastErrorObject.updatedExisting.should.be.true()
        
        # Clean up
        await testCol.deleteOne {_id: 'test_replace_before'}
      catch err
        should.not.exists(err)
        debug.error err

    it 'should maintain old format for findOneAndDelete', ()->
      try
        # First insert a test document
        await testCol.insertOne {_id: 'test_delete', value: 'to_delete', count: 0}
        
        # Test findOneAndDelete
        ret = await testCol.findOneAndDelete {_id: 'test_delete'}
        
        # Verify return structure (should be old format)
        should.exists(ret)
        should.exists(ret.ok)
        should.exists(ret.value)
        should.exists(ret.lastErrorObject)
        
        # Should return the deleted document
        ret.value.should.have.property('value', 'to_delete')
        ret.value.should.have.property('count', 0)
      catch err
        should.not.exists(err)
        debug.error err

    it 'should return correct format for findOneAndReplace', ()->
      try
        # First insert a test document
        await testCol.insertOne {_id: 'test_replace', value: 'original'}
        
        # Test findOneAndReplace
        ret = await testCol.findOneAndReplace(
          {_id: 'test_replace'},
          {_id: 'test_replace', value: 'replaced'},
          {returnDocument: 'after'}
        )
        
        # Verify return structure
        should.exists(ret)
        should.exists(ret.ok)
        should.exists(ret.value)
        should.exists(ret.lastErrorObject)
        ret.value.value.should.equal('replaced')
        ret.lastErrorObject.n.should.equal(1)
        ret.lastErrorObject.updatedExisting.should.be.true()
        
        # Clean up
        await testCol.deleteOne {_id: 'test_replace'}
      catch err
        should.not.exists(err)
        debug.error err

    it 'should not include metadata when includeResultMetadata is false', ()->
      try
        # Test for all findOneAnd* methods
        methods = ['findOneAndUpdate', 'findOneAndDelete', 'findOneAndReplace']
        
        for method in methods
          # First insert a test document
          await testCol.insertOne {_id: "test_#{method}", input: 'original'}
          
          # Prepare arguments based on method
          args = switch method
            when 'findOneAndUpdate'
              [{_id: "test_#{method}"}, {$set: {input: 'updated'}}, {includeResultMetadata: false}]
            when 'findOneAndDelete'
              [{_id: "test_#{method}"}, {includeResultMetadata: false}]
            when 'findOneAndReplace'
              [{_id: "test_#{method}"}, {_id: "test_#{method}", input: 'replaced'}, {includeResultMetadata: false}]
          
          # Execute method
          ret = await testCol[method].apply(testCol, args)
          
          # Verify return structure - should be the document itself without metadata
          should.exists(ret)

          ret.input.should.equal('original')
          # Verify the actual document was modified
          doc = await testCol.findOne {_id: "test_#{method}"}
          if method is 'findOneAndDelete'
            should.not.exists(doc)
          else if method is 'findOneAndUpdate'
            doc.input.should.equal('updated')
          else
            doc.input.should.equal('replaced')
          
          # Clean up if needed
          if method isnt 'findOneAndDelete'
            await testCol.deleteOne {_id: "test_#{method}"}
          
      catch err
        should.not.exists(err)
        debug.error err

  describe 'test stats method',->
    it 'should return collection stats', ()->
      try
        # Clean up the collection first
        await testCol.deleteMany {}
        
        # First insert some test documents
        await testCol.insertMany([
          {_id: 'stats_test1', value: 'test1'},
          {_id: 'stats_test2', value: 'test2'},
          {_id: 'stats_test3', value: 'test3'}
        ])
        
        # Get collection stats
        ret = await testCol.stats()
        
        # Verify return structure
        should.exists(ret)
        should.exists(ret.ns)
        should.exists(ret.count)
        should.exists(ret.size)
        should.exists(ret.avgObjSize)
        should.exists(ret.storageSize)
        should.exists(ret.capped)
        should.exists(ret.nindexes)
        should.exists(ret.totalIndexSize)
        
        # Verify specific values
        ret.count.should.equal(3)
        # Convert ns to string if it's not already
        ret.nindexes.should.be.above(0) # at least _id index
        
        # Clean up
        await testCol.deleteMany {_id: /^stats_test/}
      catch err
        should.not.exists(err)
        debug.error err

    it 'should return correct stats after operations', ()->
      try
        # Clean up the collection first
        await testCol.deleteMany {}
        
        # Get initial stats
        initialStats = await testCol.stats()
        initialCount = initialStats.count
        
        # Insert a document
        await testCol.insertOne {_id: 'stats_op_test', value: 'test'}
        
        # Get stats after insert
        afterInsertStats = await testCol.stats()
        afterInsertStats.count.should.equal(initialCount + 1)
        
        # Update the document
        await testCol.updateOne(
          {_id: 'stats_op_test'},
          {$set: {value: 'updated'}}
        )
        
        # Get stats after update
        afterUpdateStats = await testCol.stats()
        afterUpdateStats.count.should.equal(initialCount + 1)
        
        # Delete the document
        await testCol.deleteOne {_id: 'stats_op_test'}
        
        # Get stats after delete
        afterDeleteStats = await testCol.stats()
        afterDeleteStats.count.should.equal(initialCount)
      catch err
        should.not.exists(err)
        debug.error err

  describe 'test deprecated methods',->
    it 'should throw error for each deprecated method', ()->
      try
        deprecatedMethods = mongo4.DEPRECATED_METHODS.split('|')
        
        for method in deprecatedMethods
          # Test with callback
          await new Promise (resolve, reject)->
            testCol[method] {}, (err, ret)->
              should.exists(err)
              err.message.should.match(/no longer supported/)
              err.message.should.match(/deprecated/)
              resolve()
          
          # Test with promise
          try
            await testCol[method] {}
            throw new Error("Should have thrown error")
          catch err
            should.exists(err)
            err.message.should.match(/no longer supported/)
            err.message.should.match(/deprecated/)
      catch err
        should.not.exists(err)
        debug.error err

    it 'should handle deprecated methods with different arguments', ()->
      try
        deprecatedMethods = mongo4.DEPRECATED_METHODS.split('|')
        # Test with different argument combinations
        testCases = [
          {method: 'update', args: [{}, {$set: {test: 1}}]},
          {method: 'remove', args: [{_id: 'test'}]},
          {method: 'count', args: [{status: 'active'}]},
          {method: 'insert', args: [{_id: 'test', value: 1}]},
          {method: 'save', args: [{_id: 'test', value: 1}]},
          {method: 'findAndModify', args: [{_id: 'test'}, {$set: {value: 1}}]}
        ]

        for testCase in testCases
          try
            await testCol[testCase.method].apply(testCol, testCase.args)
            throw new Error("Should have thrown error for #{testCase.method}")
          catch err
            should.exists(err)
            err.message.should.match(/no longer supported/)
            err.message.should.match(/deprecated/)
      catch err
        should.not.exists(err)
        debug.error err
