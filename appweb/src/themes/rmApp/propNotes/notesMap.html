<link rel="stylesheet" href="https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.css">
<link rel="stylesheet" href="/css/apps/propNotes.css">
<link rel="stylesheet" href="/css/apps/previewInfo.css">
<div id="notesMap" v-cloak>
  <header id="header-bar" class="bar bar-nav">
    <a class="icon fa fa-back pull-left" href="javascript:;" @click="goBackNotes()"></a>
    {{? it.title}}
      <h1 class="title">{{=- it.title}}</h1>  
    {{??}}
      <h1 class="title">{{- Notes}}</h1>  
    {{?}}
  </header>
  <div id="notesMapProp" class="content">
    <div id="id_d_map"></div>
  </div>
  <div class="map-controls">
    <div class="map-control" @click="setMapTypeId()">
      <span class="fa" :class="mapDispMode == 'ROADMAP'?'fa-globe':'fa-map-o'"></span>
    </div>
    <div class="map-control" @click="locateMe()">
      <span class="fa fa-locate"></span>
    </div>
    <div class="map-control" >
      <span class="icon icon-plus" @click="zoomIn()"></span>
    </div>
    <div class="map-control">
      <span class="fa fa-rmminus" @click="zoomOut()"></span>
    </div>
  </div>
  <div class="scollPreview">
    <div class="displayInBl mapContent" v-for="prop in curProps">
      <preview-info v-show="showPreview" :prop="prop"></preview-info>
    </div>
  </div>
  <flash-message></flash-message>
</div>
<script src="/js/vue3.min.js"></script>
<script src="https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.js"></script>
<!-- <script src="https://api.tiles.mapbox.com/mapbox-gl-js/v1.0.0/mapbox-gl.js"></script> -->
<script src="/js/propNotes/notesMapPage.js"></script>