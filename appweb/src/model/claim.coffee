###
prop_claimed
  pid: propid
  uid: inreal agent _id
  side: coop / listing
  tp: sale/rent
  sldd: sold date（房源成交时间）
  people: [
  {
      _id: crm._id
      role: role
      source: firsthand/secondary/self
      pcOfShr:percentage of share 1-100 缺省100
      m: memo
    }
  ]
  _mt
  tsNum: date number(用于过滤时间)
  ts
###
debug = DEBUG()
ClaimCol = COLLECTION 'chome','prop_claimed'
ClaimDelCol = COLLECTION 'chome','prop_claimed_delete'
config = CONFIG(['serverBase'])
if config.serverBase?.masterMode
  ClaimCol.createIndex {'uid':1,'pid':1,'side':1}, {unique:true}
{date2num,dateNum2str} = INCLUDE 'lib.helpers_date'
ObjectId = INCLUDE('lib.mongo4').ObjectId
{toCamelCase} = INCLUDE 'lib.helpers'
{fullNameOrNickname} = INCLUDE 'libapp.user'
{replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'
{saletpIsSale} = INCLUDE 'libapp.propertiesTranslate'
gClaimRange = 1000 * 60 * 60 * 24 * 30 #30天
saleTypeMap =
  'Sld':'Sale',
  'Lsd':'Rent'
contactSource =
  'RM Direct':'Firsthand',
  'RM Referral':'Secondary',
  'Self':'Self'

getSaleType = (saletp)->
  isSale = saletpIsSale(saletp)
  if isSale
    return saleTypeMap['Sld']
  return saleTypeMap['Lsd']

getClaimTokenKeys = ()->
  keysMap = {}
  for t in Object.values(saleTypeMap)
    keys = []
    for s in Object.values(contactSource)
      keys.push "claim#{t}#{s}"
    keysMap[t] = keys
  return keysMap

buildSearchQuery = (body)->
  query = {}
  for f in ['tp','side']
    if body[f]
      query[f] = body[f].toLowerCase()
  if body.date
    date = body.date
    if body.date in ['This month','This year']
      now = new Date()
      switch body.date
        when 'This month'
          stringMonth = now.getMonth()+1
          stringMonth = if stringMonth < 10 then "0#{stringMonth}" else stringMonth
          date = "#{now.getFullYear()}#{stringMonth}"
        when 'This year'
          date = now.getFullYear()
    dateReg = new RegExp('^'+date)
    query[body.sort] = dateReg
  # 如果查询类型存在且不在有效类型列表中，则删除该查询条件
  if query.tp and (query.tp not in ['sale', 'rent'])
    delete query.tp
  return query

class Claim

###
  #生成claim相关的所有token key，以供获取sys表中定义的token值
###
  @tokenKeysMap = getClaimTokenKeys()
###
  # 生成claim保存的数据
  # @param {object} body - 待处理的数据
  # @return [array] - 根据side生成的保存的claim数据
###
  @buildUpdate:(body)->
    updateList=[]
    uid = body.uid
    pid = body.pid
    mergedIds = body.mergedIds
    mergedTopId = body.mergedTopId
    sldd = body.sldd.toString()
    tp = getSaleType(body.saletp).toLowerCase()
    ts = new Date()
    tsNum = date2num(ts).toString()
    err = null
    for k in ['coop','listing']
      continue unless v = body[k]
      update =
        query:{pid:mergedTopId,uid,side:k}
      set = {tp,ts,tsNum,sldd,m:v.m,mergedIds}
      people = []
      v.people.forEach((p) ->
        info =
          _id: p._id
          role: p.role
          source: p.source
          pcOfShr: p.pcOfShr
        people.push info
        # 多人需要考虑多个token query
        if not (p.sourceKey and p.pcOfShr)
          err = MSG_STRINGS.BAD_PARAMETER
        update.tokenQuery = {uid:uid,key:'claim'+tp.charAt(0).toUpperCase() + tp.slice(1)+p.sourceKey,\
          id:mergedTopId,memo:mergedTopId,pcOfShr:p.pcOfShr}
      )
      set.people = people
      update.set = set
      updateList.push update
    if err
      return {err}
    return updateList

###
  # 添加或更新claim数据，如果已经被别人认领，则不能认领
  # @param {objectid} uid - user._id
  # @param {body} - post传入参数
  # @param {string} [body.pid] - property数据id
  # @param {string} [body._id] - 待更新的claimed _id
  # @param {array} [body.people] - 相关人员
  # @param {string} [body.role] - 此次交易中的角色
  # @param {string} [body.tp] - 租出或售出
  # @param {string} [body.m] - memo
  # @param {string} [body.sldd] - 成交日期
  # @param {number} [body.pcOfShr] - 房源成交过程贡献占比
  # @param {array} [body.mergedIds] - merged 房源链上的房源id,包括最高等级的房源id
  # @param {string} [body.mergedTopId] - merged 房源链上最高等级的房源id
  # @param {string} [body.saletp] - 房源售卖类型，sale/rent等
  # @returns {objectId} claim._id - 保存的claimed._id
###
  @saveBaseOnPc:(uid,body)->
    body.uid = uid
    claimInfo = await @checkIfCanClaim body
    if not claimInfo.canClaim
      return {err:MSG_STRINGS.CANT_CLAIM}
    for k in ['coop','listing']
      continue unless v = body[k]
      pcOfShr = 0
      v.people.forEach((p) ->
        pcOfShr += Number(p.pcOfShr)
      )
      body[k].pcOfShr = pcOfShr
    if (body.coop?.pcOfShr > claimInfo.coop.maxPcOfShr) or
        (body.listing?.pcOfShr > claimInfo.listing.maxPcOfShr)
      return {err:MSG_STRINGS.CLAIMOVER100}
    updateList = @buildUpdate body
    if updateList.err
      return {err:updateList.err}
    returnList = []
    for update in updateList
      detail = await ClaimCol.findOneAndUpdate update.query,{$set:update.set},\
        {upsert:true,returnDocument:'after'}
      returnList.push {id:detail?.value?._id,pid:detail?.value?.pid,tokenQuery:update.tokenQuery}
    return {returnList}


###
  # 更新claim数据对应的token表_id
  # @param {objectid} id - claim._id
  # @param {objectid} tokenId - token._id
###
  @updateTokenIdById:(id,tokenId)->
    await ClaimCol.updateOne {_id:id},{$set:{tokenId:tokenId}},{upsert:false}

###
  # 检查claim必要字段，返回是否可以claim
  # @param {object} body - 待处理的数据
  # @return {Boolean} true/false
###
  @checkSaveClaim:(body)->
    if(not(body?.pid and (body.coop or body.listing)))
      return false
    canClaim = true
    for k in ['coop','listing']
      continue unless v = body[k]
      v.people.forEach((p) ->
        if p.maxPcOfShr is 0
          delete body[k]
        else if((not p.pcOfShr) or (not p._id) or (not p.source))
          canClaim = false
      )
    return canClaim

###
  # 根据uid和pid获取claimd信息
  # @param {objectid} uid - user._id
  # @param {string} [pid] - property数据id
  # @returns {object|null} - The claim object if found, or null if not found.
###
  @getById:({uid,pid})->
    query = {uid:new ObjectId(uid),mergedIds:pid}
    claimedList =  await ClaimCol.aggregate([
      {$match:query},
      {$unwind: { path:'$people',preserveNullAndEmptyArrays: true}},
      {'$project': {
        'people._id': { $toObjectId: '$people._id' },
        pid: 1,
        side: 1,
        tp: 1,
        m: 1,
        sldd: 1,
        uid:1,
        ts:1,
        'people.pcOfShr':1
        'people.source':1
        'people.role': 1
        mergedIds: 1
       }},
      {$lookup:{
        from: 'crm',
        localField:'people._id',
        foreignField: '_id',
        as: 'client',
        pipeline: [
          {$project:{nm:1}}
        ],
      }},
      {$unwind: {path: '$client',preserveNullAndEmptyArrays: true}},
      {'$project': {
        'people._id': '$client._id','people.nm': '$client.nm','people.pcOfShr':1,\
        'people.source':1,pid:1,side:1,tp:1,uid:1,m:1,ts:1,sldd:1,'people.role':1,mergedIds:1
       }},
      {
        $group: {
          _id: '$_id',
          pid:{$first:'$pid'},
          side:{$first:'$side'},
          tp:{$first:'$tp'},
          m:{$first:'$m'},
          sldd:{$first:'$sldd'},
          uid:{$first:'$uid'},
          ts:{$first:'$ts'},
          people: {  $push: '$people' }
          mergedIds: {$first:'$mergedIds'}
        }
      },
      { $sort:{side:1}},
    ])
    return null unless claimedList.length > 0
    return claimedList

###
  # 根据传入参数，搜索claimed list
  # @param {objectid} uid - user._id
  # @param {body} - post传入参数，处理'tp','role','date','sort','agent'
  # @param {boolean} isNoteAdmin - noteAdmin可以查看所有inreal agent的claimed list
  # @returns [claimedList]
###
  @getList:({uid,body,isNoteAdmin})->
    body.sort ?= 'tsNum'
    query = buildSearchQuery body
    if isNoteAdmin
      delete query.uid
      if body.agent
        query.uid = new ObjectId(body.agent)
    else
      query.uid = new ObjectId(uid)
    sort = {ts:-1,side:1}
    if body.sort is 'sldd'
      sort = {sldd:-1,ts:-1,side:1}
    return await ClaimCol.aggregate([
      {$match:query},
      {$unwind: { path:'$people',preserveNullAndEmptyArrays: true}},
      {'$project': {
        'people._id': { $toObjectId: '$people._id' },
        pid: 1,
        side: 1,
        tp: 1,
        m: 1,
        sldd: 1,
        uid:1,
        ts:1,
        'people.pcOfShr':1
        'people.source':1
        'people.role': 1
       }},
      {$lookup:{
        from: 'crm',
        localField:'people._id',
        foreignField: '_id',
        as: 'client',
        pipeline: [
          {$project:{nm:1}}
        ],
      }},
      {$unwind: {path: '$client',preserveNullAndEmptyArrays: true}},
      {$lookup:{
        from: 'user',
        localField: 'uid',
        foreignField: '_id',
        as: 'user',
        pipeline: [
          {$project:{nm:1,avt:1,nm_en:1,nm_zh:1,_id:1}}
        ],
      }},
      {$unwind: {path:'$user',preserveNullAndEmptyArrays:true}},
      {'$project': {
        'people._id': '$client._id',
        'people.nm': '$client.nm',
        'people.pcOfShr':1,
        'people.source':1,
        'people.role':1,
        pid:1,
        side:1,
        tp:1,
        m:1,
        sldd:1,
        uid:1,
        user:1
        ts:1
       }},
      {
        $group: {
          _id: '$_id',
          pid:{$first:'$pid'},
          side:{$first:'$side'},
          tp:{$first:'$tp'},
          m:{$first:'$m'},
          sldd:{$first:'$sldd'},
          ts:{$first:'$ts'},
          uid:{$first:'$uid'},
          user:{ $first: '$user'},
          people: {  $push: '$people' }
        }
      },
      { $sort:sort},
    ])

###
  # 获取claim相关的所有pid，返回pid数组
  # @param {object} {uid,body,isNoteAdmin} - 查询参数，参考getList
  # @return [pid]
###
  @getMapPids:({uid,body,isNoteAdmin})->
    body.sort ?= 'tsNum'
    query = buildSearchQuery body
    if isNoteAdmin
      delete query.uid
      if body.agent
        query.uid = new ObjectId(body.agent)
    else
      query.uid = new ObjectId(uid)
    # 只查pid字段
    docs = await ClaimCol.findToArray(query, {projection: {pid: 1}})
    pids = docs.map((d) -> d.pid)
    # 去重
    Array.from(new Set(pids))

###
  # 获取用户claim百分比信息
  # @param [array] [pid] - 搜索的所有pid
  # @returns [{
  avt:
  fnm:
  pcOfshare:
  }]
###
  @claimedPc:(ids,lang)->
    ids = Array.from(new Set(ids))
    query = {pid:{$in:ids}}
    result = await ClaimCol.aggregate([
      {$match:query},
      {$unwind: { path:'$people',preserveNullAndEmptyArrays: true}},
      {'$project': {
        side: 1,
        uid:1,
        pid:1,
        'people.pcOfShr':1
       }},
       {$lookup:{
        from: 'user',
        localField: 'uid',
        foreignField: '_id',
        as: 'user',
        pipeline: [
          {$project:{nm:1,avt:1,nm_en:1,nm_zh:1,_id:1}}
        ],
      }},
      {$unwind: {path:'$user',preserveNullAndEmptyArrays:true}},
      {'$project': {
        'people.pcOfShr':1,
        side:1,
        uid:1,
        user:1
        pid:1,
       }},
      {
        $group: {
          _id: '$_id',
          pid:{$first:'$pid'},
          side:{$first:'$side'},
          uid:{$first:'$uid'},
          user:{ $first: '$user'},
          people: {  $push: '$people' }
        }
      },
    ])
    userMap = {}
    for l in result
      user ={}
      user.fnm = fullNameOrNickname(lang,l.user)
      user.avt = replaceRM2REImagePath l.user?.avt if l.user?.avt
      user.pcOfShr = l.people.reduce(((acc, cur) -> acc + Number(cur.pcOfShr)), 0)
      userMap[l.pid]?={}
      userMap[l.pid][l.side]?={}
      userMap[l.pid][l.side][l.uid] = user
    return userMap

###
  # 删除claim信息，只有admin可以删除
  # @param {string} [id] - 删除的claim数据id
  # @returns {Promise<void>} - A promise that resolves when the claim is deleted.
###
  @deleteById:(id)->
    query = {_id:id}
    result = await ClaimCol.findOneAndDelete query
    await ClaimDelCol.insertOne result.value
    return result.value

###
  # 检查是否可以被认领
  # 暂时没有入职日期判断，统一设置 成交日期为3个月内的房源
  # @param {string} [sldd] - 成交日期
  # @param {string} [lst] - 房源成交状态
  # @returns {boolean} - 能否被claim
###
  @checkIfCanClaim:({lst,sldd,pid,uid,claimed,saletp})->
    # 如果lst为Sld或Lsd，并且成交日期在30天内，则可以认领.认领过的不判断lst和sldd
    unless claimed
      cantClaim = {canClaim:false}
      if (not lst) or (not (lst in ['Sld','Lsd','Pnd','Cld']))
        return cantClaim
      if new Date().getTime() - new Date(dateNum2str(sldd)).getTime() > gClaimRange
        return cantClaim
    canClaimMap =
      canClaim:true
      coop:{maxPcOfShr:'100'}
      listing:{maxPcOfShr:'100'}
    canClaimMap.tp = getSaleType(saletp)
    claimedList = await ClaimCol.findToArray {mergedIds:pid,uid:{$ne:uid}},{$projection:{pcOfShr:1}}
    return canClaimMap unless claimedList.length
    for claim in claimedList
      canClaimMap[claim.side].maxPcOfShr = claim.people.reduce(((acc, cur) ->\
        acc - Number(cur.pcOfShr)), canClaimMap[claim.side].maxPcOfShr or 100)
    for k in ['coop','listing']
      if canClaimMap[k].maxPcOfShr <= 0
        canClaimMap[k].maxPcOfShr = 0
    return canClaimMap

MODEL 'Claim',Claim
