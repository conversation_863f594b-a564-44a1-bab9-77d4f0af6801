<header id="masthead" class="site-header fixed" role="banner">
  <div class="header-wrap">
    <div class="container">
      <div class="row">
        <div class="{{?it.page == 'home'}}logo{{?}} col-sm-5 col-xs-12">
          <a href="{{=uri '/'}}" title="{{=- req.setting.logoAlt}}">
            <img class="site-logo" src="{{=it.logoPath || '/img/home/<USER>'}}" alt="{{=- req.setting.logoAlt}}">
          </a>
          {{? (it.page != 'home' && it.form != 'forum')}}
          <span class="header-searchbar-mobileIcon fa fa-search"></span>
          <div class="header-searchbar" id="autocompleteSearchBar">
            <span id="header-searchbar-close" class="fa fa-arrow-left"></span>
            <Search search-place-holder="{{= it.searchPlaceHolder}}" />
            {{? it.vue3}}
            <div class="search-container-input">
              <span class="fa fa-search searchInputIcon"></span>
              <input type="text" id="searchVue3" placeHolder="{{= it.searchPlaceHolder}}">
            </div>
            {{?}}
          </div>
          {{?}}
        </div>
        <div class="{{?it.page == 'home'}}header-menu{{?}} col-sm-7 col-xs-12">
          <div class="btn-menu fa fa-bars"></div>
          <nav id="mainnav" class="mainnav" role="navigation">
            <div class="menu-main-menu-container">
              <ul id="menu-main-menu" class="menu">
                {{& for (var [k,menu] of Object.entries(it.mainMenus)) { }}
                {{? menu.link}}
                <li {{? menu.className}}class="{{=menu.className}}"{{?}}>
                  {{? k == 'language'}}
                  <a class="menu-lang" href="javascript:void(0)"><span class="fa fa-globe"></span>{{= it.languages[it.lang]}} <span class="fa fa-angle-down"></span></a>
                  {{?? k == 'forum'}}
                  <a href="{{= menu.link}}" target="_blank">{{=-, menu.title,menu}}</a>
                  {{?? k == 'saves'}}
                  <a href="{{=uri menu.link}}" class="{{=(k == it.menu)?'current-page':''}}">{{=-, menu.title,settings}}</a>
                  {{??}}
                  <a href="{{=uri menu.link}}" class="{{=(k == it.menu)?'current-page':''}}">{{=-, menu.title,menu}}</a>
                  {{?}}
      
                  {{? menu.submenus}}
                    <ul class="sub-menu">
                      {{~menu.submenus :sub:i}}
                      {{?sub}}
                      <li>
                        <a class="{{= it.lang == sub.id?'activeLang':''}}" href="{{= sub.link}}" target="{{=sub.target||'_self'}}">
                          {{? k == 'language'}}
                          {{= sub.title}}
                          {{??}}
                          {{=-, sub.title,menu}}
                          {{?}}
                        </a>
                      </li>
                      {{?}}
                      {{~}}
                    </ul>
                  {{?}}
                </li>
                  {{?}}
                {{& } }}
              </ul>
            </div>
          </nav>
        </div>
      </div>
    </div>
  </div>
</header>