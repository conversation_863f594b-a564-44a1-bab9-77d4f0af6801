var propItem={props:{prop:{type:Object,default:function(){return{pnSrc:[]}}},loading:{type:Boolean},lang:{type:String},tag:{type:String,default:""},cantClick:{type:Boolean,default:!1}},mounted(){},computed:{showBlur:function(){return!("sold"!=this.tag&&"fast"!=this.tag||!this.prop.login)}},methods:{openDetail:function(s){if(!this.cantClick){var t=/^RM/.test(s.id)?s.id:s._id;openPopup(`/1.5/prop/detail/inapp?id=${t}`,this.$_("RealMaster"))}},getPropDate:s=>"U"==s.status_en?s.sldd:s.mt?s.mt.substr(0,10):"",formatPrice:s=>"number"==typeof s?"$"+(s=Math.ceil(s)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):s,popupSrcList(){window.bus.$emit("popup-match-list",this.prop)},cntSrc(s){if(!s)return 0;var t=0;return s.forEach((s=>{t+=s.v.length})),t},formateSrc(s){if(!s||"string"==typeof s)return s;var t=[];return s.forEach((s=>{s.nm?t.push(s.nm):t.push(s)})),t.join(",")},showLpInDelisted(s){return"Delisted"==s.saleTpTag_en?this.formatPrice(s.lp||s.lpr):this.formatPrice(s.sp||s.lp||s.lpr)}},template:'\n    <div class="prop" :class="loading?\'loading\':\'\'" @click="openDetail(prop)" data-sub="open detail" :data-id="prop._id">\n      <div class="detail">\n        <div class="addr one-line" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.addr">{{prop.unt}} {{prop.addr}}</span>\n        </div>\n        <div class="prov">{{prop.city}}, {{prop.prov}}</div>\n        <div class="bdrms" :class=\'{blur:showBlur}\'>\n          <span v-if="prop.bdrms"><span class="fa fa-rmbed"></span> {{prop.bdrms}}{{prop.br_plus?\'+\'+prop.br_plus:\'\'}}</span>\n          <span v-if="prop.rmbthrm || prop.tbthrms || prop.bthrms"><span class="fa fa-rmbath"></span> {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>\n          <span v-if="prop.rmgr||prop.tgr||prop.gr"><span class="fa fa-rmcar"></span> {{prop.rmgr||prop.tgr||prop.gr}}</span>\n        </div>\n        <div v-if="showBlur" class=\'prov\'>{{$_(\'Please login to see this listing!\')}}</div>\n        <div v-else>\n          <div v-if="prop.lp == \'$0\'" class="price">{{$_(\'To Be Negotiated\')}}</div>\n          <div v-else-if="tag == \'loss\'" class="price">\n            <span class=\'black\'>-{{formatPrice(prop.lsp - prop.sp)}}</span>\n            <span v-if="prop.lspDifPct && (tag == \'loss\')" class="price-change">{{Math.round(prop.lspDifPct * 10000)/100}}%</span>\n          </div>\n          <div v-else class="price">\n            <span>{{showLpInDelisted(prop)}}</span>\n            <span v-if="prop.pc && (prop.status == \'A\') && prop.pcts" class="price-change">\n              <span :class="prop.pc > 0?\'plus\':\'mins\'">{{prop.pc>0?\'+\':\'-\'}}</span>\n              <span>{{formatPrice(Math.abs(prop.pc))}}</span>\n            </span>\n            <span v-if="prop.lstStr && prop.lst == \'Sc\'" class="price-change">{{prop.lstStr}}</span>\n            <span v-if="(prop.sp && prop.priceValStrRed) && (tag != \'loss\')" class="price-change sold">{{formatPrice(prop.lp || prop.lpr)}}</span>\n          </div>\n        </div>\n        <div v-if="prop.ohdate" class="oh"><span class="fa fa-rmhistory"></span>{{$_(\'Open House\')}}: {{prop.ohdate}}</div>\n      </div>\n      <div class="img" :class=\'{blur:showBlur}\'>\n        <img class="lazy" :src="prop.thumbUrl || \'/img/no-photo.png\'" onerror="this.src=\'/img/no-photo.png\'"  />\n        <div class="tag" :class="prop.tagColor">\n          <span v-if="prop.ytvid || prop.vurlcn"><span class="fa fa-youtube-play"></span></span>\n          <span v-if="prop.ltp == \'exlisting\'" class="ltp">{{$_(\'Exclusive\')}}</span>\n          <span v-if="prop.ltp == \'assignment\'" class="ltp">{{$_(\'Assignment\')}}</span>\n          <span v-if="prop.ltp == \'rent\'" class="ltp">{{$_(\'Rental\')}}</span>\n          <span v-if="prop.type" class="type">{{prop.type}}</span>\n          <span>{{prop.saleTpTag || prop.lstStr}}</span>\n        </div>\n        <div v-if="prop.sldDom >= 0" class="dom">{{prop.sldDom}} {{$_(\'days\')}}</div>\n        \x3c!--div v-else class="dom">{{getPropDate(prop)}}</div--\x3e\n      </div>\n      <div v-if="prop.pnSrc && prop.pnSrc.length>0" class=\'match\' @click.stop=\'popupSrcList(prop)\' data-sub="feeds pnsrc" :data-id="prop._id">\n        <span class="fa fa-radar"></span>\n        <span class="pnSrc" v-if="src = prop.pnSrc[0]">\n        <span class=\'name\'>{{src.transK}} </span>\n          {{formateSrc(src.v)}}\n        </span>\n        <span class="count">{{cntSrc(prop.pnSrc)}}</span>\n        <span class="fa fa-caret-down"></span>\n      </div>\n    </div>\n  '},deals={data:()=>({page:0,waiting:!1,hasMoreProps:!1,cntTotal:0,scrollElement:null,hasMap:!0,list:[],rcmdHeight:0,dispVar:{lang:"zh-cn",isCip:!1,isLoggedIn:!1,isClaimAdmin:!1,sessionUser:{}},datas:["lang","isCip","allowedEditGrpName","isVipRealtor","isLoggedIn","isRealGroup","isApp","isClaimAdmin","isDevGroup","monthAndYearArray","sessionUser"],sortBy:["tp","date","side","agent","sort"],showClaim:!1,agent:"",agentInfo:{},tp:"",tpList:["All","Sale","Rent","Deal failed"],side:"",sideList:["All","Coop","Listing"],date:"",dateList:[],filterList:[],sort:"tsNum",showBackdrop:!1,users:[],waitEditIdx:-1}),mounted(){this.$getTranslate(this);var s=this;s.scrollElement=document.getElementById("saves-container"),s.scrollElement.addEventListener("scroll",s.listScrolled),s.rcmdHeight=parseInt(window.innerWidth/2),s.getPageData(s.datas,{},!0),bus.$on("pagedata-retrieved",(function(t){s.dispVar=Object.assign(s.dispVar,t),s.dateList=t.monthAndYearArray,s.dateList.unshift("All"),t.isClaimAdmin&&s.getInrealMembers()})),s.getClaimedList(),window.showMap||(window.showMap=this.goMap),bus.$on("close-edit",(()=>{s.showClaim=!1})),bus.$on("refresh-claim",(t=>{s.list.splice(s.waitEditIdx,1,t),s.waitEditIdx=-1}))},methods:{selectSort(s){this.sort!=s&&(this.sort=s,this.searchByFilter())},deleteClntFilter(){this.agentInfo={},this.agent="",this.searchByFilter()},addFilter(s){if(this.waitFilter==s&&(this.filterList.length||this.showAgents))return this.reset();this.waitFilter=s,"agent"==s?this.showAgents=!this.showAgents:this.filterList=this[s+"List"],this.showBackdrop=!0},selectFilter(s){this[this.waitFilter]="All"==s?"":s,this.searchByFilter(),this.reset()},selectAgent(s){this.agentInfo=s,this.agent=s._id,this.searchByFilter(),this.reset()},searchByFilter(){var s=this;s.page=0,s.filterList=[],document.querySelector(".prop-list-container")&&(document.querySelector(".prop-list-container").scrollTop=0),s.getClaimedList()},getInrealMembers(){var s=this,t={page:s.page};s.sortBy.forEach((i=>{s[i]&&(t[i]=s[i])})),fetchData("/group/getMembers",{body:t},(function(t,i){if(t||i.e)return RMSrv.dialogAlert(t||i.e);i.ok&&(s.users=i.users)}))},getCurrentPropMaxPcOfShare(s,t){fetchData("/propClaim/maxPc",{body:s},(function(s,i){var a=s||i.e;if(!a)return i.ok?t(i):void 0;window.bus.$emit("flash-message",a.toString())}))},confirmDelete(s,t){if(!this.dispVar.isClaimAdmin)return;let i=this,a=i.$_("Confirm delete?"),p=i.$_("Cancel"),e=i.$_("Yes");return RMSrv.dialogConfirm(a,(function(a){a+""=="2"&&i.deleteClaim(s,t),a+""=="1"&&(s.showDelete=!1)}),"",[p,e])},deleteClaim(s,t){if(!this.dispVar.isClaimAdmin)return;let i=this;fetchData("/propClaim/delete",{body:{id:s._id}},(function(s,a){var p=s||a.e;p?window.bus.$emit("flash-message",p.toString()):(a.ok&&(i.showClaim=!1,i.list[t+1]&&i.list[t].showDate&&(i.list[t+1].showDate=!0),i.list.splice(t,1)),a.msg&&window.bus.$emit("flash-message",a.msg))}))},reset:function(){var s=this;s.showAgents=!1,s.filterList=[],s.grpsOptions=!1,s.showFavGroups=!1,s.showBackdrop=!1},toggleFav(s){this.showFavGroups=!0,this.prop=s},getClaimedList:function(s){var t=this,i={page:t.page};t.sortBy.forEach((s=>{t[s]&&(i[s]=t[s])})),setLoaderVisibility("block"),fetchData("/propClaim/list",{body:i},(function(s,i){if(setLoaderVisibility("none"),s||i.e)return RMSrv.dialogAlert(s||i.e);t.list=i.list||[]}))},openClaim(s,t){var i=this;this.waitEdit&&s._id==this.waitEdit._id||(setLoaderVisibility("block"),this.getCurrentPropMaxPcOfShare(s,(t=>{setLoaderVisibility("none"),s.pcOfShr||(t.pcOfShr="100"),s=Object.assign({},s,t),i.waitEdit=s,window.bus.$emit("change-claim",s)}))),this.waitEditIdx=t,this.showClaim=!0},goMap(){var s="/1.5/map/notesMap?from=claim";this.sortBy.forEach((t=>{const i=this[t];void 0!==i&&""!==i&&(s+=`&${t}=${encodeURIComponent(i)}`)}));RMSrv.getPageContent(s,"#callBackString",{toolbar:!1},(function(s){}))}}};initUrlVars();var app=Vue.createApp(deals);app.component("prop-item",propItem),app.component("flash-message",flashMessage),app.mixin(baseMixins),app.mixin(pageDataMixins),trans.install(app,{ref:Vue.ref}),app.mount("#deals");
