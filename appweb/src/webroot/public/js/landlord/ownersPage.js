function time(t){return(t=new Date(t)).getMonth()+1+"/"+t.getDate()+" "+t.getHours()+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()}function day(t){if(t)return(t=new Date(t)).getUTCDate()}function yearMonth(t){if(t)return(t=new Date(t)).getFullYear()+"."+(t.getUTCMonth()+1)}function number(t,e){return null!=t?(e=parseInt(e),isNaN(t)?0:parseFloat(t.toFixed(e))):t}function propPrice(t,e){return null!=t?(t=parseInt(t),isNaN(t)||0==t?"":(t<1e3?t+="":t=t<1e4?(t/1e3).toFixed(1)+"K":t<999500?Math.round(t/1e3).toFixed(0)+"K":(t/1e6).toFixed(e=e||1)+"M",t)):""}function percentage(t,e){return null!=t?(t=parseFloat(t),isNaN(t)?0:(100*t).toFixed(2)):t}function dotdate(t,e,n="."){if(!t)return"";"number"==typeof t&&(t=(t+="").slice(0,4)+"/"+t.slice(4,6)+"/"+t.slice(6,8)),"string"!=typeof t||/\d+Z/.test(t)||(t+=" EST");var r=e?"年":n,a=e?"月":n,o=e?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(t)&&!/\d+Z/.test(t)){var i=t.split(" ")[0].split("-");return i[0]+r+i[1]+a+i[2]+o}var s=new Date(t);return!s||isNaN(s.getTime())?t:s.getFullYear()+r+(s.getMonth()+1)+a+s.getDate()+o}function datetime(t){return(t=new Date(t)).getMonth()+1+"/"+t.getDate()+"/"+t.getFullYear()+" "+t.getHours()+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()}function monthNameAndDate(t){if(!t)return"";var e=new Date(t);return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sept","Oct","Nov","Dec"][e.getMonth()]+"."+e.getDate()}function currency(t,e="$",n){try{if("string"==typeof t&&-1!=t.indexOf(e))return t;var r=parseInt(t);if(isNaN(r))return null;r<100&&n<2&&(n=2);var a=t.toString().split(".");return a[0]=a[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),0==n?a[1]=void 0:n>0&&a[1]&&(a[1]=a[1].substr(0,n)),e+a.filter((function(t){return t})).join(".")}catch(t){return console.error(t),null}}function arrayValue(t){return Array.isArray(t)?t.join(" "):t}var filters={time:time,day:day,number:number,dotdate:dotdate,datetime:datetime,propPrice:propPrice,percentage:percentage,yearMonth:yearMonth,monthNameAndDate:monthNameAndDate,currency:currency,arrayValue:arrayValue},rmsrvMixins={created:function(){"undefined"!=typeof RMSrv&&null!==RMSrv&&(this.ready=!0)},methods:{trackEventOnGoogle(t,e,n,r){trackEventOnGoogle(t,e,n,r)},exMap(t,e){let n;return this.dispVar.isApp||(document.location.href="/adPage/needAPP"),t.useMlatlng||"N"===t.daddr&&t.lat&&t.lng?n=t.lat+","+t.lng:(n=(t.city_en||t.city||"")+", "+(t.prov_en||t.prov||"")+", "+(t.cnty_en||t.cnty||""),n="N"!==t.daddr?(t.addr||"")+", "+n:n+", "+t.zip),e=(e||this.dispVar.exMapURL)+encodeURIComponent(n),RMSrv.showInBrowser(e)},goBack(){if("nativeMap"==vars.src||"nativeAutocomplete"==vars.src)return window.rmCall(":ctx::cancel");vars.d?document.location.href=vars.d:window.history.back()},sprintf(){var t=arguments,e=1;return t[0].replace(/%((%)|s|d)/g,(function(n){var r=null;if(n[2])r=n[2];else{if(r=t[e],"%d"===n)r=parseFloat(r),isNaN(r)&&(r=0);e++}return r}))},appendLocToUrl(t,e,n){if(null!=e.lat&&null!=e.lng){var r=t.indexOf("?")>0?"&":"?";return t+=r+"loc="+e.lat+","+e.lng}return t},appendCityToUrl(t,e,n={}){if(!e.o)return t;var r=t.indexOf("?")>0?"&":"?";return t+=r+"city="+e.o,e.p&&(t+="&prov="+e.p),e.n&&(t+="&cityName="+e.n),e.pn&&(t+="&provName="+e.pn),e.lat&&(t+="&lat="+e.lat),e.lng&&(t+="&lng="+e.lng),n.saletp&&(t+="&saletp="+n.saletp),null!=n.dom&&(t+="&dom="+n.dom),null!=n.oh&&(t+="&oh="+!0),n.ptype&&(t+="&ptype="+n.ptype),t},appendDomain(t){var e=window.location.href.split("/");return t=e[0]+"//"+e[2]+t},clickedAd(t,e,n,r){var a=t._id;if(t.inapp)return window.location=t.tgt;e&&trackEventOnGoogle(e,"clickPos"+n),a=this.appendDomain("/adJump/"+a),RMSrv.showInBrowser(a)},goTo(t,e={}){if(t.googleCat&&t.googleAction&&trackEventOnGoogle(t.googleCat,t.googleAction),t.t){let e=t.t;"For Rent"==t.t&&(e="Lease");var n=t.cat||"homeTopDrawer";trackEventOnGoogle(n,"open"+e)}var r=t.url,a=t.ipb,o=this;if(r){if(t.login&&!this.dispVar.isLoggedIn)return window.location="/1.5/user/login";if(!this.jumping){if(t.vipplus&&!this.dispVar.isVipPlus)return this.confirmVip();if(t.trebuser&&!this.dispVar.hasAid)return this.confirmTreb();if(t.t,"Agent"==t.t&&!t.direct)return this.dispVar.isLoggedIn?this.dispVar.isProdSales||this.dispVar.marketAdmin?window.location=t.url:null:window.location="/1.5/user/login";if("Services"==t.t)return window.location=r;if(1==a){return t.jumpUrl&&(r=t.jumpUrl+"?url="+encodeURIComponent(t.url)),this.tbrowser(r,{backButton:{image:"back",imagePressed:"back_pressed",align:"left",event:"backPressed"}})}if(3==a)return RMSrv.scanQR("/1.5/iframe?u=");if(4==a)return console.log(r),RMSrv.showInBrowser(r);if(1==t.loc){var i=this.dispVar.userCity;r=this.appendCityToUrl(r,i)}if(t.projQuery){var s=this.dispVar.projLastQuery||{};r+="?";for(let t of["city","prov","mode","tp1"])s[t]&&(r+=t+"="+s[t],r+="&"+t+"Name="+s[t+"Name"],r+="&")}if(1==t.gps){i=this.dispVar.userCity;r=this.appendLocToUrl(r,i)}1==t.loccmty&&(r=this.appendCityToUrl(r,e)),t.tpName&&(r+="&tpName="+this.$_(t.t,t.ctx)),this.jumping=!1,o.isNewerVer(o.dispVar.coreVer,"5.8.0")&&/mapSearch|autocomplete/.test(r)&&!/mode=list/.test(r)||(o.jumping=!0),setTimeout((function(){window.location=r}),10)}}},clearCache(){"undefined"!=typeof RMSrv&&null!==RMSrv&&RMSrv.clearCache&&RMSrv.clearCache()},confirmNoFnLn:function(t,e,n,r){e=e||"To be presented here, please complete your personal profile.";var a=this.$_?this.$_:this.$parent.$_,o=a(e),i=a("Later"),s=a("Do it Now");n=n||"";return RMSrv.dialogConfirm(o,(function(t){t+""=="2"?window.location="/1.5/settings/editProfile":r&&(window.location=r)}),n,[i,s])},confirmSettings:function(t,e){t=t||"To find nearby houses and schools you need to enable location";var n=this.$_?this.$_:this.$parent.$_;"function"!=typeof n&&(n=t=>t);var r=n(t),a=n("Later"),o=n("Go to settings"),i=i||"";return RMSrv.dialogConfirm(r,(function(t){t+""=="2"?RMSrv.openSettings():"function"==typeof e&&e()}),i,[a,o])},confirmNotAvailable(t){t=t||"According to the Real Estate Board notice, the sold price information is open by Oct. 22. We will keep you updated at earliest time possible.";var e=this.$_?this.$_:this.$parent.$_,n=e(t),r=e("I Know"),a=a||"";return RMSrv.dialogConfirm(n,(function(t){}),a,[r])},confirmUpgrade:function(t,e){e=e||"Only available in new version! Upgrade and get more advanced features.";var n=this.$_?this.$_:this.$parent.$_,r=n(e),a=n("Later"),o=n("Upgrade"),i=this.appendDomain("/app-download");return RMSrv.dialogConfirm(r,(function(e){t&&(i+="?lang="+t),e+""=="2"&&RMSrv.closeAndRedirect(i)}),"Upgrade",[a,o])},confirmVip:function(t,e){e=e||"Available only for Premium VIP user! Upgrade and get more advanced features.";var n=this.$_?this.$_:this.$parent.$_,r=n(e),a=n("Later"),o=n("See More");return RMSrv.dialogConfirm(r,(function(t){t+""=="2"&&RMSrv.showInBrowser("https://www.realmaster.ca/membership")}),"VIP",[a,o])},tbrowser:function(t,e={}){var n;n={toolbar:{height:44,color:"#E03131"},closeButton:{image:"close",align:"right",event:"closePressed"},fullscreen:!1},n=Object.assign(n,e),RMSrv.openTBrowser(t,n)}}},evaluateMixins={created:function(){},data:function(){return{numFields:["range","range_r","last","last_r","st_num","bdrms","bthrms","br_plus","reno","gr","tax","sqft","sqft1","sqft2","depth","front_ft"],fields:["range","lp","range_r","last","last_r","city","cnty","lng","lat","prov","addr","reno","st","st_num","cmty","mlsid","bdrms","bthrms","tp","br_plus","gr","tax","sqft","thumbUrl","unt","sqft1","sqft2","depth","front_ft","lotsz_code","irreg"]}},mounted(){},methods:{getFormatAddr:(t,e)=>t||(e.lat&&e.lng?e.lat.toString().substr(0,7)+","+e.lng.toString().substr(0,7):""),goBack(){window.history.back()},getGoolgeStreeViewImg(t,e,n){var r=t.streetView+"&location="+e.lat+","+e.lng,a=t.streetViewMeta+"&location="+e.lat+","+e.lng;this.$http.get(a).then((function(t){console.log(t),"OK"==t.body.status?n(r):n(null)}),(function(){n(null)}))},removeHist(t,e,n,r,a,o){t.stopPropagation();var i=this;RMSrv.dialogConfirm(a,(function(t){if(t+""=="2"){var a={uid:e,uaddr:n};r&&(a.id=r),fetchData("/1.5/evaluation/delete",{body:a},(function(t,e){t?(console.log(t),i.msg="error"):1==e.ok?o():(console.log(e.e),i.msg="error")}))}}),this.getTranslate("Message"),[this.getTranslate("Cancel"),this.getTranslate("Confirm")])},formatTs:t=>formatDate(t),getIds(t){var e=[];for(let n of t)e.push(n._id);return e},appendDomain(t){var e=window.location.href.split("/");return t=e[0]+"//"+e[2]+t},openHistPage(t,e){var n="/1.5/evaluation/histPage.html?uaddr="+t+"&inframe=1";if(this.fromMls&&(n+="&fromMls=1"),this.share?n+="&share=1":n+="&nobar=1",n=this.appendDomain(n),this.dispVar.isApp){var r=e.trim(25);RMSrv.openTBrowser(n,{nojump:!0,title:r})}else window.document.location.href=n},getMaxDist(){var t=this;t.$http.get("/1.5/evaluation/maxDistRange?type="+t.prop.tp).then((function(e){e&&(e=e.data,t.max_dist_range=e.max_dist_range)}),(function(){}))},compareDifference(t){var e=this;return"number"==typeof t.bdrms&&"number"==typeof e.prop.bdrms&&(t.bdrms_diff=t.bdrms-e.prop.bdrms),"number"==typeof t.bthrms&&"number"==typeof e.prop.bthrms&&(t.bthrms_diff=t.bthrms-e.prop.bthrms),"number"==typeof t.gr&&"number"==typeof e.prop.gr&&(t.gr_diff=t.gr-e.prop.gr),t.lotsz_code==e.prop.lotsz_code&&"number"==typeof t.depth&&"number"==typeof t.front_ft&&"number"==typeof e.prop.depth&&"number"==typeof e.prop.front_ft&&(t.front_ft_diff=Math.round(t.front_ft-e.prop.front_ft),t.size_diff=parseInt(t.front_ft*t.depth-e.prop.front_ft*e.prop.depth)),"number"==typeof t.sqft&&"number"==typeof e.prop.sqft?t.sqft_diff=parseInt(t.sqft-e.prop.sqft):"number"==typeof t.sqft1&&"number"==typeof t.sqft2&&"number"==typeof e.prop.sqft?(t.sqft1_diff=parseInt(t.sqft1-e.prop.sqft),t.sqft2_diff=parseInt(t.sqft2-e.prop.sqft)):"number"==typeof t.sqft1&&"number"==typeof t.sqft2&&"number"==typeof e.prop.sqft1&&"number"==typeof e.prop.sqft2&&(t.sqft1_diff=parseInt(t.sqft1-e.prop.sqft1),t.sqft2_diff=parseInt(t.sqft2-e.prop.sqft2)),t.st&&t.st==e.prop.st&&t.prov==e.prop.prov&&t.city==e.prop.city&&(t.sameStreet=!0),(vars.fromMls||e.prop.mlsid)&&!t.sameStreet&&t.cmty&&t.cmty==e.prop.cmty&&t.prov==e.prop.prov&&t.city==e.prop.city&&(t.sameCmty=!0),t},openPropModal(t){var e="/1.5/evaluation/listing.html?lat="+t.lat+"&lng="+t.lng+"&inframe=1";if(vars.share||(e+="&nobar=1"),e=RMSrv.appendDomain(e),this.dispVar.isApp){t.addr.trim(25);RMSrv.openTBrowser(e,{nojump:!0,title:t.addr})}else window.location.href=e},getPropCnt:function(t,e){var n=this;(t.uaddr||t.lat&&t.lng)&&fetchData("/1.5/evaluation/propcnt",{body:t},(function(t,r){t?(console.log(t),n.msg="error"):e(r)}))},getPropFromVars(t){t||(t=window.vars);var e={};for(let n of this.fields)t[n]&&("thumbUrl"==n?e[n]=decodeURIComponent(t[n]):this.numFields.indexOf(n)>=0?e[n]=Number(t[n]):e[n]=t[n]);return e},buildUrlFromProp(){var t=[];for(let e of this.fields)this.prop[e]&&("thumbUrl"==e?t.push(e+"="+encodeURIComponent(this.prop.thumbUrl)):t.push(e+"="+this.prop[e]));return t.join("&")}}},histCard={props:["hist"],mounted(){},methods:{getTranslate:function(t){return TRANSLATES[t]||t},remove(t){var e={e:t,hist:this.hist};window.bus.$emit("remove",e)},getTime:t=>formatDate(t),openResultModal(t){var e={e:t,hist:this.hist,fromhist:!1};window.bus.$emit("openResult",e)}},template:'\n  <div class="sp-addr">{{hist.addr}}</div>\n  <div class="bdrms"><span v-show="hist.hist.bdrms != null"><span>{{hist.hist.bdrms}}</span><span class="fa fa-rmbed"></span></span><span v-show="hist.hist.bthrms != null"><span>{{hist.hist.bthrms}}</span><span class="fa fa-rmbath"></span></span><span v-show="hist.hist.gr != null"><span>{{hist.hist.gr}}</span><span class="fa fa-rmcar"></span></span><span v-show="hist.hist.sqft != null"><span>{{hist.hist.sqft}} {{getTranslate(\'sqft\')}}</span></span></div>\n  <div class="close"><span class="icon fa fa-rmclose" @click="remove($event)"></span></div>\n  <div class="price-wrapper">\n      <div class="price">{{$filters.currency(hist.hist.result.p,\'$\',0)}}</div>\n      <div class="text">{{getTime(hist.hist.ts)}}<span v-if="hist.tracked" style="padding-left:10px;">{{getTranslate(\'Saved\')}}</span></div>\n  </div>\n  <div class="btn btn-green btn-right" @click="openResultModal($event)">{{getTranslate(\'ReEstimate\')}}</div>\n  '},Caret={props:["changeval"],data:()=>({tp:null}),mounted(){},methods:{getIconClass:function(){if("number"==typeof this.changeval){if(this.changeval>0)var t="up";else if(this.changeval<0)t="down";return`fa-caret-${t}`}}},template:'\n  <span class="caret fa" :class="getIconClass()"></span>\n  '},keyFacts={props:["cmty"],beforeMount(){},mounted(){},components:{Caret:Caret},methods:{displayMomYoY:t=>"number"!=typeof t?"N/A":`${Math.abs(t.toFixed(2))}%`,displayPrice(){return this.cmty.avgP?`$${this.cmty.avgP}`:"N/A"}},template:'\n  <div class="row">\n    <div class="data">\n      <div class="val price">{{displayPrice()}}</div>\n      <div class="nm">{{$_(\'Avg. Price\')}}</div>\n    </div>\n    <div class="data">\n      <Caret :changeval="cmty.mom"></Caret>\n      <div class="val">{{displayMomYoY(cmty.mom)}}</div>\n      <div class="nm">{{$_(\'MoM\')}}</div>\n    </div>\n    <div class="data">\n      <Caret :changeval="cmty.yoy"></Caret>\n      <div class="val">{{displayMomYoY(cmty.yoy)}}</div>\n      <div class="nm">{{$_(\'YoY\')}}</div>\n    </div>\n  </div>\n  '},dailyFeeds={props:["cmty","form"],mounted(){},components:{keyFacts:keyFacts},methods:{gotoStatPage(){var{city_en:t,pr_en:e,nm:n}=this.cmty;openContent(`/1.5/prop/stats?city=${t}&prov=${e}&cmty=${n}&d=/1.5/landlord/owners&isPopup=1&itvl=M`,{toolbar:!1})}},template:'\n    <div class="cmty-stats">\n      <div class="tl">{{cmty.nm}}</div>\n      <div class="sub">{{$_(\'Based on your subscription\')}}</div>\n      <key-facts :cmty="cmty"></key-facts>\n    </div>\n    <div class="monthly-stats" data-sub="daily feeds go trends" @click="gotoStatPage()">\n      <span>{{cmty.tsf}} - {{cmty.tst}}</span>\n      <span>{{$_(\'Trends\')}}<span class="icon icon-right-nav"></span></span>\n    </div>\n  '},owners={data:()=>({isBusy:!1,hists:[],cmtys:[],postsLength:0,calculatedSpWidth:144,dispVar:{isLoggedIn:!1,lang:"zh",isApp:!0,isCip:!1,sessionUser:{},reqHost:"",isAdmin:!1,userCity:{o:"Toronto",n:"多伦多",p:"Ontario"}},hasWechat:!0,datas:["isApp","isCip","isLoggedIn","lang","sessionUser","reqHost","userCity","isAdmin","defaultEmail"],latests:[],totalHist:0,totalTrackedProp:0,totalTrackingUsers:0,yellowpageList:[{url:"/1.5/yellowpage?tp=mortgage&d=/1.5/landlord/owners",img:"/img/staging/reno_contract.png",name:"Mortgage"},{url:"/1.5/yellowpage?tp=insur&d=/1.5/landlord/owners",img:"/img/staging/reno_solution.png",name:"Insurance"},{url:"/1.5/yellowpage?tp=bas&d=/1.5/landlord/owners",img:"/img/staging/reno_booking.png",name:"B&A Sales"},{url:"/1.5/yellowpage?tp=maint&d=/1.5/landlord/owners",img:"/img/staging/reno_construction.png",name:"Maintenance"}]}),mounted(){if(this.$getTranslate(this),window.bus){this.postsLength=document.querySelector("#postsLength").innerHTML,this.calculatedSpWidth=parseInt((window.innerWidth-30)/1.2);var t=window.bus,e=this;this.getPageData(this.datas,{},!0),t.$on("pagedata-retrieved",(function(t){e.dispVar=Object.assign(e.dispVar,t),RMSrv.hasWechat&&RMSrv.hasWechat((t=>{null!=t&&(e.hasWechat=t)}))})),window.showSignup||(window.showSignup=this.showSignup),t.$on("add-hist",(function(t){e.prop.uaddr=t.uaddr,e.histcnt++})),t.$on("remove",(function(t){e.remove(t.e,t.hist)})),t.$on("openResult",(function(t){e.openResultModal(t.e,t.hist,t.fromhist)})),this.getUserHist(),this.getFavCmty(),window.openEvaluatePage||(window.openEvaluatePage=this.openEvaluatePage),window.goTo||(window.goTo=this.goTo),loadLazyImg()}else console.error("global bus is required!")},methods:{topUp(){RMSrv.openTBrowser(this.appendDomain("/1.5/prop/topup/charge"),{nojump:!0,title:this.getTranslate("topListing")})},goToReno:t=>(t||(t="https://www.realrenovation.ca?d=/1.5/landlord/owners"),RMSrv.showInBrowser(t)),getTranslate:function(t){return TRANSLATES[t]||t},showSignup(){toggleModal("signupModal","opens")},toggleModal(t,e){toggleModal(t,e)},getFavCmty:function(){var t=this;fetchData("/1.5/community/favList",{body:{full:1}},(function(e,n){if(e||n.err)return RMSrv.dialogAlert(e||n.err);t.cmtys=n.cmtys}))},remove:function(t,e){var n=this.getTranslate("afterDelete"),r=this;this.removeHist(t,e.hist.uid,e._id,null,n,(function(){var t=r.hists.findIndex((function(t){return t._id==e._id}));r.hists.splice(t,1)}))},goCommunity(){var t="/1.5/community/filter";return t=this.appendCityToUrl(t,this.dispVar.userCity),window.location=t},getUserHist(){var t=this;fetchData("/1.5/evaluation/userHist",{body:{limit:30,tracked:!0}},(function(e,n){e?(console.log(e),t.msg="error"):1==n.ok?t.hists=n.hist||[]:(console.log(n.e),t.msg="error")}))},openResultModal(t,e,n){t.preventDefault(),t.stopPropagation();var r="/1.5/evaluation/result.html?uaddr="+encodeURIComponent(e._id)+"&id="+e.hist._id+"&lang="+this.dispVar.lang+"&d=/1.5/landlord/owners";n&&(r+="&hist=1"),r=RMSrv.appendDomain(r),window.location.href=r},openEvaluatePage(){var t="/1.5/evaluation/evaluatePage.html?nobar=1&inframe=1";t=RMSrv.appendDomain(t),RMSrv.openTBrowser(t,{nojump:!0,title:this._("Evaluation Conditions","evaluation")})},goTo(t){window.location.href=t},openEvaluatePage(){var t="/1.5/evaluation/evaluatePage.html?nobar=1&inframe=1";t=RMSrv.appendDomain(t),RMSrv.openTBrowser(t,{nojump:!0,title:this.getTranslate("Conditions")})}},computed:{spWrapperWidth(){return this.calculatedSpWidth*this.hists.length+10*(this.hists.length-1)}}},app=Vue.createApp(owners);function scrollBar(){var t,e=document.querySelector(".list-nav-container"),n=document.querySelector(".list-nav-link.selected"),r=document.querySelector(".list-nav-active"),a=n.getBoundingClientRect();t=a.left+a.width/2-window.innerWidth/2,e.scrollLeft=t,r.style.left=a.left+a.width/2-15+"px"}function goBack(t){"undefined"!=t?document.location.href=t:window.history.back()}trans.install(app,{ref:Vue.ref}),app.component("hist-card",histCard),app.component("flash-message",flashMessage),app.component("daily-feeds",dailyFeeds),app.config.globalProperties.$filters={currency:filters.currency},app.mixin(rmsrvMixins),app.mixin(pageDataMixins),app.mixin(evaluateMixins),app.mount("#owners"),window.addEventListener("load",(()=>{scrollBar()}));
