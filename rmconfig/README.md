## README
Configuration and Running Scripts

### What is this repository for?
- To run RealMaster programs with specified configurations

### How do I get set up?
- Ensure that you have downloaded appweb and set the path in local.ini srcPath (Default path is at the same level as rmconfig):

    ```
    ├── rmconfig
    │   ├── built # content generated from config -> built
    │   ├── keys # key/pem file for services
    │   ├── logs # server/batch logs
    │   └── config # basic config files
    ├── appweb # [see:](appweb/src/readMe.md)
    │   ├── batch
    │   ├── docs
    │   ├── extra
    │   ├── fileStorage
    │   ├── n5
    │   └── src
    ├── pyfpimg
    ├── pyml
    ```
- Install the packages 
  `npm install`
- Fill out the `local.ini` file as needed (for reference: `tpls/*`). The information in this file will override the original values in the generated built files. You can set personal configurations such as alertEmail, using TOML format:
You may need get your own host according to the [Using OrbStack Environment on Mac to Run the App](#using-orbstack-environment-on-mac-to-run-the-app)
    ```toml
      [contact]
      trustedAssignEmail = '<EMAIL>'
      alertSMS = '12345678'
      alertEmail = '<EMAIL>'
      projectFubEmail = '<EMAIL>'

      [importNotify]
      bcreTo = ["<EMAIL>"]

      [importNotify.defaultTo]
      defaultTo = ["<EMAIL>"]

      [dbs.chome]
      uri = "mongodb://d1:d1@************:27017,************:27018/chomeca?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"

      [dbs.rni]
      uri = "***************************************************************************************************************************************************************"

      [dbs.tmp]
      uri = "***************************************************************************************************************************************************************"

      [dbs.vow]
      uri = "mongodb://d1:d1@************:27017,************:27018/vow?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"

      [log]
      path = "./logs"

      [server]
      host = "0.0.0.0"

      [serverBase]
      base = "."
      srcPath = "../appweb/src"
      srcGoAppPath = "../go/goapp/src"

      [session.redis]
      host = "************"

      [elastic]
      cert = "/mnt/mac/opt/homebrew/Cellar/elasticsearch-full/7.17.4/libexec/elastic-stack-ca.p12"
      host = "https://************:9200"

      [pyfpimg.imageSource]
      local_path_cda = "/tmp/pic"

      [pyfpimg.kafka.request]
      bootstrap_servers = "************:9092"

      [pyfpimg.kafka.response]
      _test_topic_name = "test-fpimg-response"
      bootstrap_servers = "************:9092"

      [pyfpimg.mongo]
      host = "************"

      [azure]
      subscriptionKey = "e678a9f6c8764711849e0f17504aa696"

      [golog]
      dir = "/home/<USER>/github/go/goapp/logs"
      #level = "info"
      level = "debug"
      verbose = "verbose.log"
      info = "info.log"
      error = "error.log"
      format = "text"
      standard_output = true
    ```

- Fill out the `local.test.ini` and `local.testES.ini`file as needed when you need run unit test (for reference: `tpls/*`)

- You can test your MongoDB URI in the linux(Orb on Mac) by using the mongosh shell command.
  ```
  # mongosh "uri"
  mongosh "mongodb://d1:d1@************:27017,************:27018/chomeca?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"

  ```
- If you need it to start automatically, add the `-w  multi-user.target' parameter when running it, and then follow the given instructions to proceed.
  ```
  sh ./start.sh -w 'multi-user.target'

  # do cmd like this:
  sudo cp /your/path/rmconfig/built/etc/systemd/appweb.service /etc/systemd/system/
  sudo systemctl enable appweb
  sudo systemctl start appweb
  ```

<br>

### Running the AppWeb/Batch/UnitTest/Pyfpimg/Pyml on Linux Services

1. **Generate Configuration Files:**
   - Run `coffee config.coffee [-e '<EMAIL>,<EMAIL>']` to sort the files in the config directory and generate files in the built directory. You can set the alertEmail or it will use the contact.alertEmail in _local.ini or default "<EMAIL>,<EMAIL>"
  * This step only needs to be run when generating config files separately, such as before submitting code. The appweb and unitTest steps already include this process.

2. **Start the Service:**
   - Run appweb, you can use -c config to set the config you want to use,  or the default config file will be used for the specified type if no custom config file is provided with -c, use  
    ```
      sh start.sh

      #set config
      sh start.sh -c geocode.ini -c file_server.ini
    ```

   - Run batch, unit_test, unit_testES, pyfpimg, pyml:
    Use the -t option to specify the type of program to run:
     `batch|unit_test|unit_testES|pyfpimg|pyml` 


      ```
      # batch has to use -n option to specify the name of the batch, the service name will be like [batch@fix_floorplanId.service]
      # -cmd "lib/batchBase.coffee batch/condos/fix_floorplanId.coffee dryrun debug" this part is same as the previous method to run your batch. 
      # add -cron '*-*-* *:10:00' can automately generate timer file and run cronjob
      sh start.sh -t batch -n fix_floorplanId -cmd "lib/batchBase.coffee batch/condos/fix_floorplanId.coffee dryrun debug" [-cron '*-*-* 10:00:00']

      # unit test and unit test cron job
      sh start.sh -t unit_test [-e <EMAIL>,<EMAIL>] [-cron '*-*-* 10:00:00']

      # unit test ES and unit test ES cron job
      sh start.sh -t unit_testES [-e <EMAIL>,<EMAIL>]  [-cron '*-*-* 10:00:00']

      # pyfmimg
      sh start.sh -t pyfpimg --test

      ```


3. **Check Logs and Status with systemctl:**
   ```bash
   systemctl --user start appweb.service
   systemctl --user status appweb.service
   systemctl --user status batch@fix_floorplanId.service
   systemctl --user stop appweb.service
   journalctl --user -u appweb.service

   # Reload the service:
   systemctl --user daemon-reload
   systemctl --user restart appweb.service

   # Log files:
   ./logs/appweb_log_file
   ./logs/batch_log_file

   # Check timer
   systemctl list-timers

   # Stop timer
   systemctl --user stop batch@fix_floorplanId.timer
   systemctl --user disable batch@fix_floorplanId.timer

   # list all services
   systemctl --user list-units --type=service --no-pager

   ```

4. **Unit tests can also be run locally and support to test a specific file or folder:**
  ```
  # only compile
  ./unitTest/compile.sh -s [/home/<USER>/appweb/src]

  # run all test
  sh ./unitTest/testPassed.sh -d ./

  # only run a specific file
  sh ./unitTest/test.sh -f libapp/saveToMaster.js
  
  # only run a specific module
  sh ./unitTest/test.sh  -m propertyImport
  ```

5. **Run batch only in command line:[without -t batch]**
  ```
  sh ./start.sh -n fix_floorplanId -cmd "lib/batchBase.coffee batch/condos/fix_floorplanId.coffee dryrun debug"
  ```

6. **Setting restart service:[only restart when add -rs]**
  ```
  sh ./start.sh -n fix_floorplanId -cmd "lib/batchBase.coffee batch/condos/fix_floorplanId.coffee dryrun debug" -rs
  ```

7. **Delete systemd service:**
  ```
  # delete a specific service
  sh ./start.sh -delete -t batch -n fix_floorplanId

  # delete all failed services
  sh ./start.sh -del_failed
  ```
<br>


### Using OrbStack Environment on Mac to Run the App

Developers running the application on their Mac need to use OrbStack. Follow the steps below to set up the environment:

**Note:**
If you have <b>Cloudflare WRAP</b> / <b>WRAP+</b> or <b>Quantumult(X)</b> / <b>ShadowRocket</b> / <b>WireGuard</b> / <b>TailScale</b> or any other <b>VPN</b> / <b>Zero Trust</b> client that may change the system routing table, make sure they are turned off or they allow specific traffic (<b>198.19.0.0/16</b>) between your Mac and VM. 
<b>Check this if you can have mongo connectivity issue (timeout)</b>

1. **[Mac] Install OrbStack:**
   - Run the command: `brew install orbstack` to install the software.

2. **[Mac] Set Up the Machine:**
   - Use the graphical client to create a machine with Rocky Linux version 9, or follow the [documentation](https://docs.orbstack.dev/machines/) to install via command line according to your system.

3. **[Mac] Access Rocky Linux:**
   - Run the command: `ssh orb` to access Rocky Linux. ***Note that files in Rocky Linux are isolated from files on your Mac in this mode, you need do all next steps in this mode***.

4. **[Orbstack] Install Required Software:**
   - Install Node.js, CoffeeScript, and MongoDB shell(mongosh) by running the respective installation commands.
   - Install Golang by following the [official instructions](https://go.dev/dl/)

5. **[Orbstack] Git Code from repo:**
   - Git clone `appweb` and `rmconfig` repo in your orbstack.
   - Install packages for both repo.

6. **[Mac+Orbstack] Identify Your IP:**
   - After installing Rocky using OrbStack, identify your IP addresses.
     ```bash
     # On Mac:
     ifconfig | grep "inet "
         # inet 127.0.0.1 netmask 0xff000000
         # inet ************* netmask 0xffffff00 broadcast *************
         # inet ************ netmask 0xffffff00 broadcast **************

     # On Rocky:
     ip a | grep "inet "
         # inet 127.0.0.1/8 scope host lo
         # inet *************/24 brd ************** scope global dynamic noprefixroute eth0

     # Note the IP starting with 198.19: it is probably the bridged network in your Rocky.
     # Locate the bridged network IPs with the same CIDR. For example:
     # ************ - Mac
     # ************* - Rocky
     ```

7. **[Mac] Configure MongoDB:**
   - Revise `mongod.conf` to bind to your Mac bridged IP.
     ```bash
     vi /opt/homebrew/etc/mongod.conf
     net:
       bindIp: ************, 127.0.0.1 # Use your own IP 198.19.***
     ```
   - Restart MongoDB: `brew services restart mongodb-community`.
   NOTE: this may not work if you did not install mongo using brew, use kill and manually start mongo

8. **[Mac] Update MongoDB Replica Set Configuration**
    - If you have modified the `bindIP` of any member, ensure that you also update that member's address in the replica set configuration. You can connect to the replica set using the `mongo shell` and execute the following command:

      ```javascript
      rs.conf()  // View the current replica set configuration
      ```

    - If you need to update the address of a replica set member, use the following commands:

      ```
      cfg = rs.conf()
      cfg.members[0].host = "************:27017"  // Update the member's host as needed
      rs.reconfig(cfg)
      ```

    - After completing this, the MongoDB replica set will bind to the specified IP address instead of `127.0.0.1`.

9. **[Orbstack] Install `mongodb-database-tools` which is used in unitTest**
    - Add MongoDB repository to your system [Rocky9]:
      ```bash
      sudo tee /etc/yum.repos.d/mongodb-org-7.0.repo <<EOF
      [mongodb-org-7.0]
      name=MongoDB Repository
      baseurl=https://repo.mongodb.org/yum/redhat/\$releasever/mongodb-org/7.0/x86_64/
      gpgcheck=1
      enabled=1
      gpgkey=https://www.mongodb.org/static/pgp/server-7.0.asc
      EOF
      ```
    - Use `dnf` to install the necessary tools:
      ```bash
      sudo dnf install -y mongodb-database-tools
      ```
    - ubuntu can install according to [Mongo Doc](https://www.mongodb.com/zh-cn/docs/database-tools/installation/installation-linux/)
      ```
      wget https://fastdl.mongodb.org/tools/db/mongodb-database-tools-ubuntu2404-arm64-100.10.0.deb
      sudo apt install ./mongodb-database-tools-ubuntu2404-arm64-100.10.0.deb
      ```

10. **[Mac] Configure Redis:**
    - Revise `redis.conf` to bind using your Mac bridged IP and disable protected mode. If you installed Redis with Homebrew, the configuration file is likely at `/opt/homebrew/etc/redis.conf` (for Apple Silicon) or `/usr/local/etc/redis.conf` (for Intel). Or you can use `brew info redis` to get it.
      ```bash
      vi /opt/homebrew/etc/redis.conf
      # Add redis to listen on your bridged network NIC:
      bind ************ 127.0.0.1 ::1  # Use your own IP 198.19.***
      # Disable protected mode
      protected-mode no
      ```
    - Run Redis: `redis-server /opt/homebrew/etc/redis.conf`
    - If appweb throw "Redis Store Retry", check 
    `[session.redis] host = "************"`

11. **[Mac] Configure Elasticsearch:**
    - Edit the Elasticsearch configuration to allow Rocky Linux access to ES on Mac.
      ```bash
      vim /opt/homebrew/etc/elasticsearch/elasticsearch.yml
      # In the network section, force ES to listen on your bridged network NIC:
      network.host: ************  # Use your own IP 198.19.***
      # In the discovery section, add the following line to force single-node mode:
      discovery.type: single-node
      comment out:
      #cluster.initial_master_nodes: ["your-laptop-2.local"]
      ```

12. **[Mac] Configure Kafka:**
    - Edit `server.properties` to bind Kafka to your Mac bridged IP.
      ```bash
      vi /opt/homebrew/etc/kafka/server.properties
      # revise listeners
      listeners=PLAINTEXT://************:9092 # Use your own IP 198.19.***
      brew services restart kafka
      ```

13. **[Mac] Configure Server:**
    - Copy template ini file
      ```
      cp ./tpls/local/local.ini ./
      ```
    - Revise `local.ini` to set the server host.
      ```bash
      vi local.ini
      [server]
      host = "0.0.0.0"
      ```
    - Change `/etc/hosts` on your machine to assign hosts. For example:
      ```bash
      *************   app.test  # Use your own Rocky IP 198.19.***
      *************   www.test  # Use your own Rocky IP 198.19.***
      ```

14. **[Mac] Open OrbStack Environment Files Using VS Code:**
    - Install the [Remote - SSH](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-ssh) extension and Open the Remote Explorer panel on the left side of VS Code.
    Click the + icon to add a new remote.
    Enter `ssh orb` when prompted to add the OrbStack host.

15. **[Mac] Install GitLens Extensions in VS Code:**
    - If you need visually git blame, install `GenLens-Git supercharged` by Gitkraken in your VS Code.

If you need to use MongoDB, PostgreSQL, Elasticsearch, or Kafka, ensure they use the same configurations in their respective config files or set them in your own `local.ini`.

### appweb config using
- old: `config = CONFIG(true)`
- new [ **add config section names in list**]: `config = CONFIG(['contact', 'mailEngine','fcmv1_config'])`
<br>
- old: `module.exports._config = _config = (cfg)->
  verbose = cfg.mockMail?.verbose`
- new [ **add config section names in module.exports._config_sections**]: `module.exports._config_sections = ['mailEngine']
module.exports._config = _config = (cfg)->verbose = cfg.mailEngine?.mockMail?.verbose`

### Install in China
1. Install git  `yum install git`
2. Install homebrew   `https://developer.aliyun.com/mirror/homebrew`
3. Npm install: need use VPN
4. If you meet error: `can't find sharp model`, need to `npm install sharp` 
5. If you meet toml install error, try to install using `https://github.com/fxfred/toml.git`


### Common Unit Test Errors and Solutions
1. Error:connect ETIMEDOUT **************:80
  copy {"_id" : "China-IP-APNIC"} document from chomeca.sysdata to your test chome like dataTest.sysdata

2. Error:missing prov_city_cmty collection
  dump prov_city_cmty from your appweb db and restore in your test db

3. Error: jq: command not found
  Install jq in your rocky:`sudo dnf install jq`

4. Error: mongorestore fail
  Install MongoDB shell(mongosh) in your rocky, you can do this according step9 above ([Orbstack] Install `mongodb-database-tools` which is used in unitTest)


# FAQ
## Q: ./start.sh not wroking?
A:    
```bash
ps -ef | grep app_web
kill -s USR2 503979
```