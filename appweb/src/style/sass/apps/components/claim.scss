@import './btnSet.scss';
@import './flashMessage.scss';
@import './_prop.scss';
@import '../../base';
[v-cloak] {
  display: none;
}
#dealsEdit{
  .prop{
    padding: 15px;
  }
}
.prop-claim {
  z-index: 15;
  padding-bottom: 70px !important;
  .table-view-cell {
    border: 0 !important;
    padding-right: 15px;
    .name {
      font-size: 17px;
      color: #333;
      font-weight: bold;
    }
    &.between{
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .btn-sets {
    padding: 11px 15px;
  }
  .black{
    color: #333 !important;
    font-weight: bold;
  }
  .addClient {
    display: flex;
    align-items: center;
    padding: 5px 15px;
    height: auto;
    color: #666;
    .name {
      font-size: 17px;
      color: #333;
      font-weight: bold;
    }
    .claimed{
      flex: 1;
      max-height: 42px;
      overflow: hidden;
    }
    input:focus-visible {
      outline: 0px;
    }
  }
  .images{
    position: relative;
    max-width: 100px;
    margin: 0 auto;
    img{
      width: 20px;
      height: 20px;
      position: absolute;
      border-radius: 50%;
      top: -10px;
      z-index: 0;
      border: 2px solid #fff;
    }
  }
  .size12{
    font-size: 12px;
  }
  .editClientName {
    text-overflow: ellipsis;
    flex: 1;
    padding: 0 10px;
    overflow: hidden;
    white-space: nowrap;
    font-size: 15px;
    & > span{
      display: block;
    }
    span.source{
      font-size: 12px;
      line-height: 14px;
    }
  }
  .edit-client{
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border: 1px solid #aaa;
    border-radius: 3px;
    padding: 3px 0;
    height: 42px;
    width: 180px;
  }
  .edit-holder{
    flex: 1;
  }
  .sprite16-3-6{
    margin: 0 5px 0 10px;
  }
  .memo{
    flex: 1;
    margin: 5px 0;
    padding: 0 5px 0 10px;
    color: #333;
    height: 20px;
    font-size: 15px;
    text-overflow: ellipsis;
    resize: none;
    word-wrap: break-word;
    background: transparent;
    border: 0;
    white-space: initial;
    outline: none;
  }
  .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
  }
  .bar-footer {
    padding: 0;
    border-top: 0.5px solid rgb(245, 245, 245);
    display: flex;
    height: 50px;
  }
  .btn-fill{
    height: 50px;
    font-size: 18px;
  }
  .edit-role{
    padding: 9.5px;
    font-size: 14px;
    background: #f5f5f5;
    border: 1px solid #aaa;
    border-radius: 3px;
    width: 77px;
    height: 42px;
    margin-left: 10px;
  }
  .fa-rmclose{
    color:#aaa;
    padding: 13px 10px;
    padding-right: 0;
  }
  .select-role{
    z-index: 15;
  }
}

#schoolQuickFilter {
	font-size: 14px;
	color: #666;
	top: 86px;
	display: flex;
	align-items: center;
  padding: 0 10px;
	.icon {
		padding: 4px 1px 0 0;
		font-size: 15px;
		font-weight: normal;
		margin-top: 0px;
		padding-left: 3px;
		color: #999;
		vertical-align: top;
		padding-top: 3px;
		display: inline-block;
	}
	>div {
		display: inline-block;
		width: 25%;
		font-size: 15px;
		font-weight: 700;
		padding: 10px 0;
		vertical-align: middle;
		text-align: center;
		color: black;
    text-transform: capitalize;
    white-space: nowrap;
    &>*{
      vertical-align: middle;
    }
	}
	.cityName {
		display: inline-block;
		text-align: right;
		max-width: calc(100% - 17px);
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}
}


#deals{
  height: 100%;
  .prop{
    padding: 0;
  }
  .prop-list-container{
    padding-top: 86px;
    height: 100%;
    overflow: scroll;
  }
  .prop-list{
    background: #fff;
  }
  .clnt {
    border-radius: 3px;
    color: #000;
    padding: 3px 6px 3px 10px;
    font-size: 15px;
    vertical-align: top;
    border: 1px solid #ddd;
    display: inline-block;
    flex-shrink:1;
    margin-right: 5px;
    .name {
      font-size: 16px;
      vertical-align: middle;
      max-width: calc(100vw - 300px);
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .remove {
      vertical-align: middle;
      font-size: 19px;
      padding: 1px 0 0 3px;
      color: #666;
    }

  }
  .date-block {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background-color: #f1f1f1;
    padding:10px 15px;
    &.fixedDate {
      z-index: -1;
      position: relative;
    }
    &.fixed {
      width: 100%;
      @include flexbox();
      position: sticky;
      top: $headerHeightApp;
      left: 0;
      z-index: 10;
      justify-content: space-between;
      .group{
        font-size: 12px;
        line-height: 14px;
        text-align: right;
        color: #6f6f6f;
      }
      .date{
        margin-left: 5px;
        vertical-align: middle;
      }
    }
    &.feedTag {
      top: 105px;
    }
    .circle {
      border: 4px solid #f92324;
      border-radius: 50%;
      width: 15px;
      height: 15px;
      display: inline-block;
      vertical-align: middle;
    }
    .date {
      margin: 0 5px 0 10px;
    }
  }
  .filter{
    position: relative;
    .filterList{
      position: fixed;
      top: 130px;
      width: 100%;
      z-index: 8;
      text-transform: capitalize;
      max-height: calc(100vh - 181px);
      overflow: auto;
    }
  }
  .count{
    font-size: 20px;
    margin-right: 10px;
    flex-shrink: 0;
    span{
      font-size: 12px;
    }
  }
  .total{
    position: fixed;
    top: 130px;
    padding: 0 15px 10px;
    background: #fff;
    width: 100%;
    display: flex;
    align-items: center;
    z-index: 5;
    .left{
      flex: 1;
      display: flex;
      align-items: center;
      white-space: nowrap;
    }
    .sort{
      border: 1px solid #ccc;
      border-radius: 3px;
      white-space: nowrap;
      span{
        padding: 4px 1px;
        font-size: 13px;
        text-align: center;
        vertical-align: middle;
        color: #333;
        background-color: #fff;
        display: inline-block;
        width: 80px;
        &.select{
          background-color: #ccc;
        }
        &:not(:first-child){
          margin-left: -5px;
          border-left: 1px solid #ccc;
        }
      }
    }
  }
  .backdrop{
    z-index: 4;
  }
}
.agent-info {
  display: flex;
  align-items: center;
  padding: 15px 15px 10px;
  img{
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .info {
    flex: 1;
    padding: 0 10px;
    font-size: 12px;
    .name{
      font-size: 14px;
      font-weight: bold;
      color: #666;
    }
    .other-info {
      color: #999;
      line-height: 14px;
      padding-top: 5px;
    }
  }
  .edit-btn{
    font-size: 14px;
    & > div > *{
      padding: 10px;
    }
  }
}
.deal-failed-notice{
  padding: 5px 15px;
  font-size: 15px;
  color: #333;
  font-weight: bold;
  .fa-exclamation-circle{
    color: #e03131;
    margin-right: 5px;
  }
}
.claim-list{
  border-top: 0.5px solid #f5f5f5;
  padding-bottom: 5px;
}
.stakeholders{
  padding: 5px 15px;
  font-size: 15px;
  color: #333;
  >div{
    padding-bottom: 5px;
  }
  .stakeholders-role{
    font-weight: bold;
    color: #000;
  }
}
