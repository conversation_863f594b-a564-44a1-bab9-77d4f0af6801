function scrollBar(){var t=document.querySelector(".list-nav-container");if(t){var e,n=document.querySelector(".list-nav-link.selected"),o=document.querySelector(".list-nav-active"),i=n.getBoundingClientRect();e=i.left+i.width/2-window.innerWidth/2,t.scrollLeft=e,o.style.left=i.left+i.width/2-15+"px"}}function goBack(t){t?document.location.href=t:window.history.back()}function goMapView(){window.showMapView&&window.showMapView()}function goMap(){window.showMap&&window.showMap()}function gotoStatistic(){RMSrv.getPageContent("/1.5/notes/statistic","#callBackString",{toolbar:!0},(function(t){}))}window.addEventListener("load",(()=>{scrollBar()}));var baseMixins={mounted(){},methods:{serializeData:function(t={}){if("object"!=typeof t.data)return"";var e=t.data,n=t.prefix,o="";for(var i in e){""!=o&&(o+="&");var a=e[i];null!=a&&null!=a||(a=null),o+=n+"-"+i+"="+encodeURIComponent(a)}return o},getTranslate:function(t){return TRANSLATES[t]||t},confirmVip:function(t,e){e=e||"vipTip";var n=this.getTranslate(e),o=this.getTranslate("Later"),i=this.getTranslate("SeeMore");return RMSrv.dialogConfirm(n,(function(t){t+""=="2"&&RMSrv.showInBrowser("https://www.realmaster.ca/membership")}),"VIP",[o,i])}}};
