
{{&
  var {css,js,tabs,id,d,isPopup,isNoteAdmin} = it;
}}
{{~ css :c:idx}}
<link rel="stylesheet" href="/css{{= c}}">
{{~}}
<div id="saves-container" class="saved-page">
  <div style="display:none" id="rmPageBarColor">:name:mainTheme</div>
  {{* 'components/loader' }}
  <header id="header-bar" class="bar bar-nav {{? it.appmode == 'rm'}}rm{{?}}{{? it.appmode == 'mls'}}mls{{?}}">
    {{? d != '/1.5/index'}}
    <a class="icon fa fa-back pull-left" onClick="goBack2({isPopup:{{=isPopup}},d:'{{= d}}'})"></a>
    {{?}}
    <h1 class="title">{{-, Saved,settings}}</h1>

    {{? id == 'properties'}}
    <a href="javascript:;" onClick="goMapView()" class="pull-right mode-switch">
      <span class="">{{- MAP}}</span>
    </a>
    {{?}}
    {{? id == 'searches' || id == 'communities' || id == 'locations'}}
    <a href="/1.5/settings/notification?d=/1.5/saves/{{= id}}" class="pull-right mode-switch">
      <span class="fa fa-rmalert-o"></span>
    </a>
    {{?}}
    {{? (id == 'notes' || id == 'deals')}}
      <a href="javascript:;" onClick="goMap()" class="pull-right mode-switch">
        <span>{{- MAP}}</span>
      </a>
      {{? isNoteAdmin && id == 'notes'}}
      <a href="javascript:;" onClick="gotoStatistic()" class="pull-right mode-switch">
        <span>{{- Statistic}}</span>
      </a>
      {{?}}
    {{?}}
  </header>
  <nav class="saves-list-nav">
    <div class="list-nav-container">
      <div class="list-nav-active"></div>
      {{~ tabs :tab:idx}}
      <a class="list-nav-link {{? id == tab.selected}}selected{{?}}" href="{{=uri tab.url}}?d={{=d}}{{? isPopup}}&isPopup={{=isPopup}}{{?}}">{{=- tab.nm}}</a>
      {{~}}
    </div>
  </nav>
  <div id="{{= id}}" class="saved-page" v-cloak>
    {{* 'saves/{{= id}}' it}}
    {{? d == '/1.5/index'}}
      <div style="height:70px"></div>
    {{?}}
    <flash-message></flash-message>
  </div>
  {{? d == '/1.5/index'}}
    {{* 'bottomNav' it}}
  {{?}}
</div>
<script src="/js/stdfn.min.js"></script>
{{? id == 'notes'}}
<script src="/js/lz-string.min.js"></script>
{{?? id == 'searches'}}
<script src="/js/objectAssignDeep.min.js"></script>
{{?}}

{{~ js :j:idx}}
<script type="text/javascript" src="/js{{= j}}"></script>
{{~}}
