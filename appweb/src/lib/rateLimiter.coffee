### 
DEPRECATED!!!


Limit Access Rate Automatically
Collect every 47s:
 1. as = access/session
 2. avgas = avg(access/session)/ip
 3. s = session/ip

block Criteria:
  for any ip
    if s > 20 && avgas < 2
  for any session
    if as > max(70, avg(max(as)) * 2)

###
util = require 'util'
verbose = 0

AlertHandler = (msg)-> console.error msg

defaultConfig =
  AccessPerSessionLimit: 230
  SessionPerIPThreshold: 10
  AvgAccessPerSessionIP: 1.1
  FormInputPerIPThreshold: 4
  FormInputPerSessionThreshold: 3
  AllowSearchEngine: true

returnError = (resp)->
  resp.send '',403

accessPerSessions = {}
sessionInIPs = {}
blockedIPs = {}
# blacklist is on persistent level, in db, init on startup, and dynamic updated from request
blackListIPs = {}
blockedSessions = {}
formInputPerSessions = {}
formInputPerIPs = {}
formBlockedIPs = {}
formBlockedSessions = {}

module.exports = (opts)->
  opts = Object.assign {},defaultConfig,opts
  checkAccessRate = ->
    blocked = ""
    for ip,sessions of sessionInIPs
      session_cnt = 0
      access_cnt = 0
      for sid,cnt of sessions
        session_cnt++
        access_cnt += cnt
      avgas = access_cnt / session_cnt
      if (session_cnt > opts.SessionPerIPThreshold) and (avgas < opts.AvgAccessPerSessionIP)
        blocked += " " + ip
        blockedIPs[ip] = true
        AlertHandler "Blocked IP[#{ip}] Sessions:#{session_cnt} AvgAccessPerSession:#{avgas}",false,true
    for sid,slog of accessPerSessions
      if slog.c > opts.AccessPerSessionLimit
        blocked += " " + sid
        blockedSessions[sid] = true
        AlertHandler "Blocked Session[#{sid}]#{slog.c} user:#{slog.eml||slog.uid} ver:#{slog.ver} ua:#{slog.ua} IPs:#{util.inspect(slog.ips)}",false,true
    if blocked
      console.log "rateLimiter blocked:" + blocked
      console.log util.inspect sessionInIPs
      console.log util.inspect accessPerSessions
    accessPerSessions = {}
    sessionInIPs = {}
    formInputPerSessions = {}
    formInputPerIPs = {}

  countAndReturnFormInputBlocked = ->
    block = false
    # check ip
    ip = @remoteIP()
    if formBlockedIPs[ip]
      return true
    if not formInputPerIPs[ip]?
      formInputPerIPs[ip] = 1
    if (formInputPerIPs[ip]++) >= opts.FormInputPerIPThreshold
      AlertHandler \
        "Blocked IP[#{ip}] FormInput #{formInputPerIPs[ip]} > #{opts.FormInputPerIPThreshold}",\
        false,true
      formBlockedIPs[ip] = true
      return true
    # check session
    unless sid = @session?.id()
      return false
    if formBlockedSessions[sid]
      return true
    if not formInputPerSessions[sid]?
      formInputPerSessions[sid] = 1
      return false
    if (formInputPerSessions[sid]++) >= opts.FormInputPerSessionThreshold
      AlertHandler \
        """Blocked Session[#{sid}] FormInput #{formInputPerSessions[sid]}\
         > #{opts.FormInputPerSessionThreshold}""",\
        false,true
      formBlockedSessions[sid] = true
      return true
    return false

  resetBlockedIPs = ->
    blockedIPs = {}
    blockedSessions = {}
    formBlockedIPs = {}
    formBlockedSessions = {}
  setInterval checkAccessRate, 47000 # 47s
  setInterval resetBlockedIPs, (3600000 * 24) # 1 day



  return (req, resp, next)->
    ip = req.remoteIP()
    # console.log '+++++blackListIPs',blackListIPs
    if blackListIPs[ip]
      return returnError resp

    if blockedIPs[ip]
      return returnError resp

    # insert counter function
    if not req.countAndReturnFormInputBlocked
      req.countAndReturnFormInputBlocked = countAndReturnFormInputBlocked

    if opts.AllowSearchEngine and /google|bing|baidu|yahoo/i.test req.UA()
      return next()

    unless sid = req.session?.id()
      return next()
    if blockedSessions[sid]
      return returnError resp

    sessionLog = (accessPerSessions[sid] ?= {ips:{},c:0,ver:req.cookies?['apsv'],ua:req.UA()})
    req.getLoggedInUid req, (uid)->
      if uid
        sessionLog.uid = uid
      sessionLog.c++
      if sessionLog.ips[ip]
        sessionLog.ips[ip]++
      else
        sessionLog.ips[ip] = 1

      sessions = (sessionInIPs[ip] ?= {})
      if sessions[sid]
        sessions[sid]++
      else
        sessions[sid] = 1

      return next()

module.exports.setAlertHandler = (handler)-> AlertHandler = handler
# got data from db, add to memory
module.exports.initBlackListIPs = (data)->
  for k of data
    if /^[0-9]+\_/.test(k) or /[a-fA-F\d]{1,4}:/.test(k)
      k = k.replace(/\_/g,'.')
      blackListIPs[k] = true
module.exports.addBlackListIP = (ip)->
  blackListIPs[ip] = true