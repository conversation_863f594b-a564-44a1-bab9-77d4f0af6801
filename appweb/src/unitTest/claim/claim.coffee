# coffee ./setup.coffee
# ./mate4.sh compile
# ./test.sh -f claim/claim.js

should = require('should')
helpers = require('../00_common/helpers')
request = null
ClaimModel = null
PropertiesModel = null
describe 'claim', ->
  before (done) ->
    @timeout(300000)
    request = helpers.getServer()
    ClaimModel = helpers.MODEL 'Claim'
    debug = helpers.DEBUG()
    PropertiesModel = helpers.MODEL 'Properties'
    dbs = [
      { db: 'chome', table: 'login' },
      { db: 'chome', table: 'user' },
      { db: 'chome', table: 'rm_group' },
      { db: 'vow', table: 'properties' },
      { db: 'chome', table: 'prop_claimed' },
      { db: 'chome', table: 'token' },
    ]
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, () ->
        helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie)->
          if err
            console.log 'error when login'
          cookie.push 'apsv=appDebug'
          helpers.userCookies.admin = cookie
          helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie1)->
            cookie1.push 'apsv=appDebug'
            helpers.userCookies.user = cookie1
            helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie2)->
              cookie1.push 'apsv=appDebug'
              helpers.userCookies.inreal = cookie2
              helpers.userCookies.nouser = null
              setTimeout(done, 2500)
    return

  describe '/propClaim/delete correct',->
    tests = [
      {
        desc: 'claim and delete TRBW5473866'
        post:{
          listing: {
            side: 'listing',
            m: '123',
            people: [
              {
                role: 'Seller',
                source: 'RM Referral',
                pcOfShr: '100',
                _id: '657c20f778c55e4c52c95071',
                sourceKey: 'Secondary'
              }
            ]
          },
          pid : 'TRBW5473866'
          lst: 'Lsd'
          sldd : new Date(new Date().getTime()-1000*60*60*24*15).toFormat 'YYYYMMDD'
          onlyUnitTest:true
        },
        expected:{ok: 1, msg: 'Claimed'}
        deleteExpected:{ok: 1, msg: 'Disclaimed'}
      }
    ]
    tests.forEach (test) ->
      it test.desc, (done)->
        @timeout(300000)
        request
          .post('propClaim/edit')
          .set('Accept', 'application/json')
          .set('Cookie', ['apsv=appDebug'])
          .set('Cookie', helpers.userCookies.inreal)
          .expect('Content-Type', /json/)
          .send(test.post)
          .end (err, list) ->
            for k,v of test.expected
              list.body[k].should.be.exactly(v)
            disClaimId = list.body.ids[0]
            request
              .post('propClaim/delete')
              .set('Accept', 'application/json')
              .set('Cookie', ['apsv=appDebug'])
              .set('Cookie', helpers.userCookies.admin)
              .expect('Content-Type', /json/)
              .send({id:disClaimId})
              .end (err, list) ->
                for k,v of test.deleteExpected
                  list.body[k].should.be.exactly(v)
                done()
            return
        return

  describe '/propClaim/edit',->
    tests = [
      {
        desc: 'claim TRBW5475032'
        post:{
          pid : 'TRBW5475032'
          lst: 'Sld'
          sldd : new Date().toFormat 'YYYYMMDD'
          coop: {
            side: 'coop',
            m: '123',
            people: [
                {
                    role: 'Buyer',
                    source: 'RM Direct',
                    pcOfShr: '60',
                    _id: '5f9c25d55d2bb0601b9c141a',
                    sourceKey: 'Firsthand'
                }
            ]
          },
          onlyUnitTest: true
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ ok: 1, msg: 'Claimed'}
          admin:{ e: 'The total claimed amount of the property is above 100%.', ok: 0 }
        }
      },
      {
        desc: 'Bad Parameter without pid'
        post:{
          lst: 'Lsd'
          sldd : 20230515
          coop: {
            side: 'coop',
            m: '123',
            people: [
                {
                    role: 'Buyer',
                    source: 'RM Direct',
                    pcOfShr: '100',
                    _id: '5f9c25d55d2bb0601b9c141a',
                    sourceKey: 'Firsthand'
                }
            ]
          },
          onlyUnitTest: true
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ e: 'Bad Parameter', ok: 0 }
          admin:{ e: 'Bad Parameter', ok: 0 }
        }
      },
      {
        desc: 'Bad Parameter without role'
        post:{
          pid : 'TRBW5473866'
          lst: 'Lsd'
          sldd : 20230515,
          onlyUnitTest: true
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ e: 'Bad Parameter', ok: 0 }
          admin:{ e: 'Bad Parameter', ok: 0 }
        }
      },
      {
        desc: 'cant claim lst error'
        post:{
          pid : 'TRBW5473866'
          sldd : new Date(new Date().getTime()-1000*60*60*24*80).toFormat 'YYYYMMDD'
          coop: {
            side: 'coop',
            m: '123',
            people: [
                {
                    role: 'Buyer',
                    source: 'RM Direct',
                    pcOfShr: '100',
                    _id: '5f9c25d55d2bb0601b9c141a',
                    sourceKey: 'Firsthand'
                }
            ]
          },
          onlyUnitTest: true
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ e: "This property can't be claimed.", ok: 0 }
          admin:{ e: "This property can't be claimed.", ok: 0 }
        }
      }
      {
        desc: 'cant claim lst error'
        post:{
          pid : 'TRBW5473866'
          lst: 'New'
          sldd : new Date(new Date().getTime()-1000*60*60*24*80).toFormat 'YYYYMMDD'
          coop: {
            side: 'coop',
            m: '123',
            people: [
                {
                    role: 'Buyer',
                    source: 'RM Direct',
                    pcOfShr: '100',
                    _id: '5f9c25d55d2bb0601b9c141a',
                    sourceKey: 'Firsthand'
                }
            ]
          },
          onlyUnitTest: true
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ e: "This property can't be claimed.", ok: 0 }
          admin:{ e: "This property can't be claimed.", ok: 0 }
        }
      }
      {
        desc: 'cant claim sldd > 30days'
        post:{
          pid : 'TRBW10433488'
          lst: 'Lsd'
          sldd : 20230515
          coop: {
            side: 'coop',
            m: '123',
            people: [
                {
                    role: 'Buyer',
                    source: 'RM Direct',
                    pcOfShr: '100',
                    _id: '5f9c25d55d2bb0601b9c141a',
                    sourceKey: 'Firsthand'
                }
            ]
          },
          onlyUnitTest: true
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ e: "This property can't be claimed.", ok: 0 }
          admin:{ e: "This property can't be claimed.", ok: 0 }
        }
      }
      {
        desc: 'cant claim without people._id'
        post:{
          pid : 'TRBW5473866'
          sldd : new Date(new Date().getTime()-1000*60*60*24*80).toFormat 'YYYYMMDD'
          coop: {
            side: 'coop',
            m: '123',
            people: [
                {
                    role: 'Buyer',
                    source: 'RM Direct',
                    pcOfShr: '100',
                    sourceKey: 'Firsthand'
                }
            ]
          },
          onlyUnitTest: true
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ e: 'Bad Parameter', ok: 0 }
          admin:{ e: 'Bad Parameter', ok: 0 }
        }
      }
      {
        desc: 'cant claim without people.pcOfShr'
        post:{
          pid : 'TRBW5473866'
          sldd : new Date(new Date().getTime()-1000*60*60*24*80).toFormat 'YYYYMMDD'
          coop: {
            side: 'coop',
            m: '123',
            people: [
                {
                    role: 'Buyer',
                    source: 'RM Direct',
                    _id: '5f9c25d55d2bb0601b9c141a',
                    sourceKey: 'Firsthand'
                }
            ]
          },
          onlyUnitTest: true
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ e: 'Bad Parameter', ok: 0 }
          admin:{ e: 'Bad Parameter', ok: 0 }
        }
      }
      {
        desc: 'cant claim without people.source'
        post:{
          pid : 'TRBW5473866'
          sldd : new Date(new Date().getTime()-1000*60*60*24*80).toFormat 'YYYYMMDD'
          coop: {
            side: 'coop',
            m: '123',
            people: [
                {
                    role: 'Buyer',
                    pcOfShr: '100',
                    _id: '5f9c25d55d2bb0601b9c141a',
                    sourceKey: 'Firsthand'
                }
            ]
          },
          onlyUnitTest: true
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ e: 'Bad Parameter', ok: 0 }
          admin:{ e: 'Bad Parameter', ok: 0 }
        }
      }
    ]
    users = ['nouser','user','inreal','admin']
    tests.forEach (test) ->
      users.forEach (user) ->
        it user+' '+test.desc, (done)->
          @timeout(300000)
          request
            .post('propClaim/edit')
            .set('Accept', 'application/json')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', helpers.userCookies[user])
            .expect('Content-Type', /json/)
            .send(test.post)
            .end (err, list) ->
              expected = test.expectedList[user]
              for k,v of expected
                list.body[k].should.be.exactly(v)
              done()
          return

  describe '/propClaim/delete error',->
    tests = [
      {
        desc: 'delete claim without id'
        post:{}
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Bad Parameter', ok: 0}
          inreal:{ e: 'Bad Parameter', ok: 0}
        }
      },
      {
        desc: 'disclaim role error'
        post:{
          id:'657c20f778c55e4c52c95071'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0}
          inreal:{ e: 'Not Authorized', ok: 0}
        }
      }
    ]
    users = ['nouser','user','inreal']
    tests.forEach (test) ->
      users.forEach (user) ->
        it user+' '+test.desc, (done)->
          @timeout(300000)
          request
            .post('propClaim/delete')
            .set('Accept', 'application/json')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', helpers.userCookies[user])
            .expect('Content-Type', /json/)
            .send(test.post)
            .end (err, list) ->
              expected = test.expectedList[user]
              for k,v of expected
                list.body[k].should.be.exactly(v)
              done()
          return


  describe '/propClaim/edit',->
    tests = [
      {
        desc: 'admin claim BRER2877564'
        post:{
          pid : 'BRER2877564'
          lst: 'Sld'
          sldd : new Date(new Date().getTime()-1000*60*60*24*25).toFormat 'YYYYMMDD'
          listing: {
            side: 'listing',
            m: '123',
            people: [
              {
                role: 'Seller',
                source: 'RM Referral',
                pcOfShr: '100',
                _id: '657c20f778c55e4c52c95071',
                sourceKey: 'Secondary'
              }
            ]
          },
          onlyUnitTest: true
        }
        expected:{ok: 1, msg: 'Claimed'}
      },
      {
        desc: 'admin claim BRER2877679'
        post:{
          pid : 'BRER2877679'
          lst: 'Lsd'
          sldd : new Date(new Date().getTime()-1000*60*60*24*10).toFormat 'YYYYMMDD'
          listing: {
            side: 'listing',
            m: '123',
            people: [
              {
                role: 'Seller',
                source: 'RM Referral',
                pcOfShr: '100',
                _id: '657c20f778c55e4c52c95071',
                sourceKey: 'Secondary'
              }
            ]
          },
          onlyUnitTest: true
        }
        expected:{ok: 1, msg: 'Claimed'}
      },
      {
        desc: 'claim TRBW5473866'
        post:{
          pid : 'TRBW5473866',
          lst: 'Lsd',
          sldd : new Date(new Date().getTime()-1000*60*60*24*20).toFormat 'YYYYMMDD'
          coop: {
            side: 'coop',
            m: '123',
            people: [
                {
                    role: 'Buyer',
                    source: 'RM Direct',
                    pcOfShr: '100',
                    _id: '5f9c25d55d2bb0601b9c141a',
                    sourceKey: 'Firsthand'
                }
            ]
          },
          listing: {
            side: 'listing',
            m: '123',
            people: [
              {
                role: 'Seller',
                source: 'RM Referral',
                pcOfShr: '100',
                _id: '657c20f778c55e4c52c95071',
                sourceKey: 'Secondary'
              }
            ]
          },
          onlyUnitTest: true
        },
        expected:{ok: 1, msg: 'Claimed'}
      },
    ]
    tests.forEach (test) ->
      it test.desc, (done)->
        @timeout(300000)
        request
          .post('propClaim/edit')
          .set('Accept', 'application/json')
          .set('Cookie', ['apsv=appDebug'])
          .set('Cookie', helpers.userCookies.admin)
          .expect('Content-Type', /json/)
          .send(test.post)
          .end (err, list) ->
            for k,v of test.expected
              list.body[k].should.be.exactly(v)
            done()
        return


  describe '/propClaim/list',->
    tests = [
      {
        desc: 'all list'
        post:{}
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0 }
          inreal:{ length: 1, ok: 1 }
          admin:{ length: 5, ok: 1 }
        }
      },
      {
        desc: 'sort by sldd'
        post: {
          sort : 'ts'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0 }
          inreal:{ length: 1, ok: 1 }
          admin:{ length: 5, ok: 1 }
        }
      },
      {
        desc: 'sort by sldd'
        post: {
          sort : 'sldd'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0 }
          inreal:{ length: 1, ok: 1 }
          admin:{ length: 5, ok: 1 }
        }
      },
      {
        desc: 'filter by date sldd'
        post: {
          date: new Date().getFullYear().toString()
          sort: 'sldd'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0 }
          inreal:{ length: 1, ok: 1 }
          admin:{ above: 0, ok: 1 }
        }
      },
      {
        desc: 'filter by date This month'
        post: {
          date: 'This month'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0 }
          inreal:{ above: 0, ok: 1}
          admin:{ above: 2, ok: 1}
        }
      },
      {
        desc: 'filter by date This year'
        post: {
          date: 'This year'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0 }
          inreal:{ above: 0, ok: 1}
          admin:{ above: 2, ok: 1}
        }
      },
      {
        desc: 'filter by side'
        post: {
          side: 'coop'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0 }
          inreal:{ length: 1, ok: 1 }
          admin:{ length: 2, ok: 1 }
        }
      },
      {
        desc: 'filter by tp'
        post: {
          tp: 'Sale'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0 }
          inreal:{ length: 1, ok: 1 }
          admin:{ length: 3, ok: 1 }
        }
      },
      {
        desc: 'filter by agent'
        post: {
          agent: '601a99501d0ae231345daf14'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          user:{ e: 'Not Authorized', ok: 0 }
          inreal:{ e: 'Not Authorized', ok: 0 }
          admin:{ length: 1, ok: 1 }
        }
      },
    ]
    users = ['nouser','user','inreal','admin']
    tests.forEach (test) ->
      users.forEach (user) ->
        it user+' '+test.desc, (done)->
          @timeout(300000)
          request
            .post('propClaim/list')
            .set('Accept', 'application/json')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', helpers.userCookies[user])
            .expect('Content-Type', /json/)
            .send(test.post)
            .end (err, list) ->
              expected = test.expectedList[user]
              if list.body.ok
                if expected.above isnt undefined
                  list.body.list.length.should.be.above(expected.above)
                else
                  list.body.list.length.should.be.exactly(expected.length)
              else
                for k,v of expected
                  list.body[k].should.be.exactly(v)
              done()
          return

  describe 'get claim status',->
    tests = [
      {
        desc: 'get with user claimed'
        data:{
          uid:'601a99501d0ae231345daf14',
          pid:'TRBW5475032'
        },
        expected:{has:true}
      },
      {
        desc: 'get without user claimed'
        data:{
          uid:'6013b9d25cc88f19fc7c134a',
          pid:'BRER2877679'
        },
        expected:{has:true}
      }
      {
        desc: 'get without user claimed'
        data:{
          uid:'6013b9d25cc88f19fc7c134a',
          pid:'TRBW5475032'
        },
        expected:{has:false}
      }
    ]
    tests.forEach (test) ->
      it test.desc, ()->
        @timeout(300000)
        try
          claim = await ClaimModel.getById test.data
        catch err
          console.error err
          should.not.exists err
          return
        if test.expected.has
          should.exists claim[0]._id
        else
          should.not.exists claim
        return
# unittest
  describe '/propClaim/detail 检查是否claimed',->
    tests = [
      {
        desc: 'can claim BRER2877564'
        post:{
          id : 'BRER2877564'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          inreal: {ok : 1}
        }
      },
      {
        desc: 'can claim BRER2877679'
        post:{
          id : 'BRER2877679'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          inreal:{ok : 1}
        }
      },
      {
        desc: 'claimed TRBW5475032'
        post:{
          id : 'TRBW5475032'
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          inreal:{ok : 1,cantClaim:true}
        }
      },
      {
        desc: 'claimed TRBW5473866'
        post:{
          id : 'TRBW5473866',
        }
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          inreal: {ok : 1}#超过30天
        }
      },
      {
        desc: 'claim no id'
        post:{}
        expectedList:{
          nouser: { e: 'Need login', ok: 0 }
          inreal:{ e: 'Bad Parameter', ok: 0 }
        }
      },
    ]
    users = ['nouser','inreal']
    tests.forEach (test) ->
      users.forEach (user) ->
        it user+' '+test.desc, (done)->
          @timeout(300000)
          request
            .post('propClaim/detail')
            .set('Accept', 'application/json')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', helpers.userCookies[user])
            .expect('Content-Type', /json/)
            .send(test.post)
            .end (err, list) ->
              expected = test.expectedList[user]
              if expected.e
                for k,v of expected
                  list.body[k].should.be.exactly(v)
              else
                list.body.ok.should.be.exactly(1)
                if expected.cantClaim
                  list.body.list.length.should.be.above(0)
                else
                  for k,v of expected
                    list.body.prop.claimInfo.canClaim.should.be.exactly(true)
              done()
          return

  describe 'findMergedTopPropAsids', ->
    data = [
      {
        desc: 'findMergedTopPropAsids DDF27652197',
        id: 'DDF27652197'
        result:
          mergedTopId: 'CAR40677779'
          aSIDs: [
            { sid: '*********', id: 'TRB*********' },
            { sid: '40677779', id: 'CAR40677779' },
            { sid: '40677779', id: 'TRB*********' },
            { sid: '40677779', id: 'DDF27652197' }
          ]
          extraInfo:
            "CAR40677779": {}
      },
      {
        desc: 'findMergedTopPropAsids TRB*********',
        id: 'TRB*********'
        result:
          mergedTopId: 'CAR40677779'
          aSIDs: [
            { sid: '*********', id: 'TRB*********' },
            { sid: '40677779', id: 'CAR40677779' },
            { sid: '40677779', id: 'TRB*********' },
            { sid: '40677779', id: 'DDF27652197' }
          ]
          extraInfo:
            "CAR40677779": {}
      },
      {
        desc: 'findMergedTopPropAsids TRBW10433488',
        id: 'TRBW10433488'
        result:
          mergedTopId: 'TRBW10433488'
          aSIDs: []
          extraInfo:
            TRBW10433488:{sldd: 20230515}
      }
    ]
    data.forEach (test)->
      it test.desc, ()->
        @timeout(30000)
        try
          result = await PropertiesModel.findMergedTopPropAsids test.id,{sldd:1}
        catch err
          debug.error err
          return
        should.deepEqual(test.result,result)
        return

###############################################
# 以下为 ClaimModel 新增/未覆盖方法的单元测试
###############################################

###
# @function ClaimModel.buildUpdate
# @description 测试 buildUpdate 方法的参数校验和返回结构，确保生成的 updateList 正确。
# <AUTHOR>
###
describe 'ClaimModel.buildUpdate', ->
  it 'should return correct updateList for valid input', ->
    body = {
      uid: '601a99501d0ae231345daf14',
      pid: 'TRBW5473866',
      mergedIds: ['TRBW5473866'],
      mergedTopId: 'TRBW5473866',
      sldd: 20240101,
      saletp: 'Sale',
      coop: {
        side: 'coop',
        m: 'test',
        people: [
          {
            _id: '5f9c25d55d2bb0601b9c141a',
            role: 'Buyer',
            source: 'RM Direct',
            pcOfShr: '100',
            sourceKey: 'Firsthand'
          }
        ]
      }
    }
    updateList = ClaimModel.buildUpdate(body)
    updateList.should.be.an.Array()
    updateList.length.should.be.above(0)
    updateList[0].should.have.properties(['query','set','tokenQuery'])

  it 'should return error if people missing sourceKey or pcOfShr', ->
    body = {
      uid: '601a99501d0ae231345daf14',
      pid: 'TRBW5473866',
      mergedIds: ['TRBW5473866'],
      mergedTopId: 'TRBW5473866',
      sldd: 20240101,
      saletp: 'Sale',
      coop: {
        side: 'coop',
        m: 'test',
        people: [
          {
            _id: '5f9c25d55d2bb0601b9c141a',
            role: 'Buyer',
            source: 'RM Direct'
            # 缺少 pcOfShr/sourceKey
          }
        ]
      }
    }
    updateList = ClaimModel.buildUpdate(body)
    updateList.should.have.property('err')


###
# @function ClaimModel.checkIfCanClaim 扩展测试
# @description 测试 checkIfCanClaim 方法的各种边界情况和新增逻辑
# <AUTHOR>
###
describe 'ClaimModel.checkIfCanClaim extended tests', ->
  it 'should reject claim for BRE/CAR source with non-Cld status', ->
    @timeout(30000)
    try
      result = await ClaimModel.checkIfCanClaim({
        lst: 'Dft',
        sldd: new Date().toFormat('YYYYMMDD'),
        pid: 'TEST123',
        uid: '601a99501d0ae231345daf14',
        claimed: false,
        saletp: 'Sale',
      })
    catch err
      console.error err
      should.not.exists err
      return
    result.canClaim.should.be.exactly(false)
    return

  it 'should allow claim for BRE/CAR source with Cld status', ->
    @timeout(30000)
    try
      result = await ClaimModel.checkIfCanClaim({
        lst: 'Cld',
        sldd: new Date().toFormat('YYYYMMDD'),
        pid: 'TEST123',
        uid: '601a99501d0ae231345daf14',
        claimed: false,
        saletp: 'Sale',
      })
    catch err
      console.error err
      should.not.exists err
      return
    result.canClaim.should.be.exactly(true)
    return

###
# @function ClaimModel.getClaimTokenKeys
# @description 测试 getClaimTokenKeys 方法，确保生成的 token key map 正确。
###
describe 'ClaimModel.getClaimTokenKeys', ->
  it 'should return token keys map with Sale and Rent', ->
    keysMap = ClaimModel.tokenKeysMap
    keysMap.should.have.properties(['Sale', 'Rent'])
    keysMap['Sale'].should.match(/claimSale/)
    keysMap['Rent'].should.match(/claimRent/)

###
# @api POST /propClaim/list 测试 Deal failed 筛选功能
# @description 测试前端 Deal failed 筛选逻辑，虽然后端不处理，但需要确保接口正常返回数据
# <AUTHOR>
###
describe '/propClaim/list with Deal failed filter simulation', ->
  it 'should return all claims when no tp filter is applied', (done) ->
    @timeout(30000)
    request
      .post('propClaim/list')
      .set('Accept', 'application/json')
      .set('Cookie', ['apsv=appDebug'])
      .set('Cookie', helpers.userCookies.admin)
      .expect('Content-Type', /json/)
      .send({})  # 不传 tp 参数，模拟 Deal failed 筛选时的请求
      .end (err, res) ->
        res.body.ok.should.be.exactly(1)
        res.body.should.have.property('list')
        res.body.list.should.be.an.Array()
        # 验证返回的数据包含 prop 字段和 isFailed 字段
        if res.body.list.length > 0
          for item in res.body.list
            if item.prop
              item.prop.should.have.property('isFailed')
              item.prop.isFailed.should.be.a.Boolean()
        done()
    return


###
# @api POST /propClaim/map
# @description 测试 propClaim.coffee 新增的 map 接口，覆盖权限、参数、返回结构等。
# <AUTHOR>
###
describe '/propClaim/map', ->
  users = ['nouser', 'user', 'inreal', 'admin']
  tests = [
    {
      desc: 'should reject if not logged in',
      user: 'nouser',
      post: {},
      expected: { ok: 0, e: 'Need login' }
    },
    {
      desc: 'should reject if user is not inreal or admin',
      user: 'user',
      post: {},
      expected: { ok: 0, e: 'Not Authorized' }
    },
    {
      desc: 'should return items for inreal',
      user: 'inreal',
      post: {},
      expected: { ok: 1, items: true }
    },
    {
      desc: 'should return items for admin',
      user: 'admin',
      post: {},
      expected: { ok: 1, items: true }
    },
    {
      desc: 'should reject if agent param for inreal',
      user: 'inreal',
      post: { agent: '601a99501d0ae231345daf14' },
      expected: { ok: 0, e: 'Not Authorized' }
    },
    {
      desc: 'should return items for admin with agent param',
      user: 'admin',
      post: { agent: '601a99501d0ae231345daf14' },
      expected: { ok: 1, items: true }
    }
  ]
  tests.forEach (test) ->
    it test.user + ' ' + test.desc, (done) ->
      @timeout(30000)
      request
        .post('propClaim/map')
        .set('Accept', 'application/json')
        .set('Cookie', ['apsv=appDebug'])
        .set('Cookie', helpers.userCookies[test.user])
        .expect('Content-Type', /json/)
        .send(test.post)
        .end (err, res) ->
          if test.expected.ok is 1
            res.body.ok.should.be.exactly(1)
            res.body.should.have.property('items')
          else
            res.body.ok.should.be.exactly(0)
            res.body.e.should.match(new RegExp(test.expected.e, 'i'))
          done()
      return

###
# @api POST /propClaim/edit 测试新增的参数处理
# @description 测试认领时正确处理 sp、ptype、src 参数
# <AUTHOR>
###
describe '/propClaim/edit with required parameters', ->
  it 'should handle claim', (done) ->
    @timeout(30000)
    testData = {
      pid: 'TRBW5473866',
      lst: 'Sld',
      sldd: new Date().toFormat('YYYYMMDD'),
      listing: {
        side: 'listing',
        m: 'test with required params',
        people: [
          {
            role: 'Seller',
            source: 'RM Referral',
            pcOfShr: '50',
            _id: '657c20f778c55e4c52c95071',
            sourceKey: 'Secondary'
          }
        ]
      },
      onlyUnitTest: true
    }

    request
      .post('propClaim/edit')
      .set('Accept', 'application/json')
      .set('Cookie', ['apsv=appDebug'])
      .set('Cookie', helpers.userCookies.admin)
      .expect('Content-Type', /json/)
      .send(testData)
      .end (err, res) ->
        res.body.ok.should.be.exactly(1)
        res.body.msg.should.be.exactly('Claimed')
        done()
    return

  it 'should test BRE source with Cld status', (done) ->
    @timeout(30000)
    testData = {
      pid: 'TRBW5473866',
      lst: 'Cld',
      sldd: new Date().toFormat('YYYYMMDD'),
      listing: {
        side: 'listing',
        m: 'test BRE Cld',
        people: [
          {
            role: 'Seller',
            source: 'RM Referral',
            pcOfShr: '100',
            _id: '657c20f778c55e4c52c95071',
            sourceKey: 'Secondary'
          }
        ]
      },
      onlyUnitTest: true
    }

    request
      .post('propClaim/edit')
      .set('Accept', 'application/json')
      .set('Cookie', ['apsv=appDebug'])
      .set('Cookie', helpers.userCookies.admin)
      .expect('Content-Type', /json/)
      .send(testData)
      .end (err, res) ->
        res.body.ok.should.be.exactly(1)
        res.body.msg.should.be.exactly('Claimed')
        done()
    return
