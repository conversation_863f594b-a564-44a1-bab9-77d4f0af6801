function Mapbox(e){e=e||{},this.map=null,this.geocoder=null,this.Umarker=null,this.views={street:"mapbox://styles/mapbox/streets-v11",satellite:"mapbox://styles/mapbox/satellite-v9"},this.init=t=>{const r=e.center||[-79.393086,43.741119],n={style:e.style||this.views.street,container:t,center:r,zoom:e.zoom||10,zoomControl:e.zoomControl||!1,mapTypeControl:e.mapTypeControl||!1,scaleControl:e.scaleControl||!1,streetViewControl:e.streetViewControl||!1,rotateControl:!1,gestureHandling:"greedy",disableDefaultUI:!0,dragRotate:null==e.dragRotate||e.dragRotate,touchZoomRotate:null==e.touchZoomRotate||e.touchZoomRotate};this.map=new mapboxgl.Map(n),e.locateMe&&this.map.addControl(new mapboxgl.GeolocateControl({positionOptions:{enableHighAccuracy:!0},trackUserLocation:!0})),e.navControl&&this.map.addControl(new mapboxgl.NavigationControl)},this.onceResize=()=>{this.map.once("idle",(e=>{this.resize()}))},this.idle=e=>{this.map.on("idle",(()=>{e.forEach((e=>{"resize"==e&&this.resize()}))}))},this.resize=()=>{this.map.resize()},this.updateStyle=()=>{this.map.getStyle().sprite.indexOf("street")>-1?this.map.setStyle(this.views.satellite):this.map.setStyle(this.views.street)},this.updateZoom=e=>{"+"==e?this.map.zoomIn():this.map.zoomOut()},this.markerDefaultClass="mapboxgl-marker mapboxgl-marker-anchor-center marker-label ",this.getMarkerLabelClass=e=>{const t=e.isToplisting?"top":"";var r=this.markerDefaultClass;return e.isCenter&&(r+=" selected "),r+t},this.renderBasicMarker=(e,t)=>{let r=e.position;if(!r)return;let n=e.className,a=e.id,o=e.innerHTML,s=e.popupHTML,i=document.createElement("div");i.className=n||this.markerDefaultClass,a&&i.setAttribute("id",a),o&&(i.innerHTML=o),t&&i.addEventListener("click",(e=>{t(e)}));let l=new mapboxgl.Marker({element:i,anchor:"bottom"});return l.setLngLat(r).addTo(this.map),s&&l.setPopup(new mapboxgl.Popup({autoPan:!0}).setHTML(s)),l},this.renderMarker=(e,t,r,n,a)=>{let o={position:[e.lng,e.lat],className:this.getMarkerLabelClass(e),id:e._id,innerHTML:e.marker_price,popupHTML:t};return this.renderBasicMarker(o,(e=>{r(),n(e.target.getAttribute("id")),a.$apply(),e.stopPropagation()}))},this.renderCondoMarker=(e,t,r,n,a,o)=>{const s=e[0];let i={position:[s.lng,s.lat],className:this.getMarkerLabelClass(s),id:r,innerHTML:'<span id="'+r+'" class="marker-number">'+e.length+"</span>",popupHTML:t};return this.renderBasicMarker(i,(e=>{n(e.target.getAttribute("id")),o.$apply(),e.stopPropagation()}))},this._locating=function(t){if(e.sendMsg)return e.sendMsg("locating",t)},this.setCenter=function(e){this.map&&this.map.flyTo({center:e,zoom:10})},this._showUmarker=function(e){e=[e[1],e[0]],this._locating(!1),this.Umarker&&this.Umarker.remove();let t={position:e,className:"mapboxgl-marker mapboxgl-marker-anchor-center marker-umarker ",id:"me"};return this.Umarker=this.renderBasicMarker(t),this.setCenter(e),this._mapReady(e)},this._mapReady=function(t){var r;if(r=this.map.getCenter().wrap()||t,e.sendMsg)return e.sendMsg("locationReady",r)},this.locateMe=(e,t)=>{var r;return this._locating(!0),(r=this._getSavedGPSLoc())&&(this._showUmarker(r),t&&r&&t(r)),this._realLocateMe(e,t)},this._realLocateMe=(e,t)=>{var r,n,a;n=this,a=function(e){var r;if((r=[])[0]=e.coords.latitude,r[1]=e.coords.longitude,n._showUmarker(r),localStorage.lastGPSLoc=r[0]+","+r[1],t)return t(r)},r=function(e){if(n.Umarker&&n.Umarker.remove(),n._locating(!1),t)return t()},e&&!Object.keys(e)||(e={enableHighAccuracy:!0,timeout:15e3}),RMSrv.getGeoPosition(e,(function(e){e.err?r(e.err):a(e)}))},this._getSavedGPSLoc=()=>{var e,t;return(e=localStorage.lastGPSLoc)?function(){var r,n,a,o;for(o=[],r=0,n=(a=e.split(",")).length;r<n;r++)t=a[r],o.push(parseFloat(t));return o}():null},this.fitMarkers=function(e){var t=new mapboxgl.LngLatBounds;for(let r of e)t.extend(r.getLngLat());this.map.fitBounds(t,{padding:80})},this.parseGeoResult=function(e){if(!e&&!e.result)return{};if(ret={},r=e.result,ret.address=r.place_name,ret.lat=r.center[1],ret.lng=r.center[0],ret.place_id=r.id,ret.addr="",r.address&&(ret.st_num=r.address,ret.addr=ret.st_num),r.text&&(ret.st=r.text,ret.addr=ret.addr+" "+ret.st),r.context)for(let e of r.context)(id=e.id)&&(id.indexOf("postcode")>=0&&(ret.zip=e.text),id.indexOf("region")>=0&&(ret.prov=e.text),id.indexOf("place")>=0&&(ret.city=e.text),id.indexOf("country")>=0&&(ret.cnty=e.text));return ret},this.initAutocomplete=function(e,t){let r=15;localStorage.mapZoom&&(r=parseInt(localStorage.mapZoom));let n=new MapboxGeocoder({accessToken:mapboxgl.accessToken,countries:"CA",limit:3,types:"address",marker:!1,mapboxgl:mapboxgl,language:"en",zoom:r}),a=document.getElementById(e),o=this;return a?(a.appendChild(n.onAdd(this.map)),n.on("result",(function(e){let r=o.parseGeoResult(e);t&&t(r)})),n):(console.error("element:"+e+"is not found"),null)}}window.key&&key.mapboxKey?mapboxgl.accessToken=key.mapboxKey:console.error("No mapbox key found");var map_mixins={created:function(){},methods:{getMapObject:function(e){e||(e={});var t={isIOS:function(){return/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream},generalElementFromOption:function({icon:e,label:t,image:r,zIndex:n,elementType:a="backgroundImg",style:o,url:s}){if("img"==a)return(i=document.createElement("img")).style="width:30px;height:30px;padding:2.5px",o&&(i.style=o),i.setAttribute("src",e),i;if("div"==a){let e=document.createElement("div"),r=document.createTextNode(t);return e.appendChild(r),e.style="position:absolute;top:30px;left:10px",e}var i=document.createElement("div"),l=[45,25];s=s||"/img/mapmarkers/price.png",o="",n=1;e&&(e.url&&(s=e.url),e.scaledSize&&(l=e.scaledSize),e.addedStyle&&(o=e.addedStyle),e.zIndex&&(n=e.zIndex));var p="",m="black",d="11px";return t&&(t.text&&(p=t.text),t.color&&(m=t.color),t.fontSize&&(d=t.fontSize)),p&&(i.innerHTML=p),i.style=`width:${l[0]+"px"};height:${l[1]+"px"};background-image:url('${s}');color:${m};font-size:${d};background-size:cover; text-align: center; z-index:${n};`+o,i},init:function(t){var r;return r=(window.innerHeight||screen.height)-(null!=e.hfHeight?e.hfHeight:126),this.el=document.getElementById(t),this.el.style.height=r+"px",this.initMap(t)},initMap:function(t){var r,n,a,o,s,i;if(13,vars.loc?("string"==typeof vars.loc&&(r=function(){var e,t,r,n;for(n=[],e=0,t=(r=vars.loc.split(",")).length;e<t;e++)s=r[e],n.push(parseFloat(s));return n}()),this.mapCenter=[r[1],r[0]],"string"==typeof vars.zoom&&(i=parseInt(vars.zoom)||13),this.mapZoom=i):(r=[43.72199,-79.45175],(n=(null!=(o=document.getElementById("loc"))?o.value:void 0)||localStorage.lastMapLocZoom||localStorage.mapLoc)&&(r=function(){var e,t,r,a;for(a=[],e=0,t=(r=n.split(",")).length;e<t;e++)s=r[e],a.push(parseFloat(s));return a}(),vars.zoom&&(r[2]=parseInt(vars.zoom)||null),this.mapZoom=r[2]||(localStorage.mapZoom?parseInt(localStorage.mapZoom):13),this.mapCenter=[r[1],r[0]]),this.mapCenter=[r[1],r[0]],null==this.mapZoom&&(this.mapZoom=13)),a={center:this.mapCenter,zoom:this.mapZoom,style:null,draggable:!0,scaleControl:!0,disableDoubleClickZoom:!1,mapTypeControl:!1,streetViewControl:!1,zoomControl:!1,sendMsg:e.sendMsg,dragRotate:!1},null!=e.mapTypeControl&&(a.mapTypeControl=!!e.mapTypeControl),this.mapbox=new Mapbox(a),this.mapbox.init(t),this.mapbox.onceResize(),this.gmap=this.mapbox.map,this.cluster={},vars.loc&&vars.cMarker&&!e.noCmarker){var l={position:this.mapCenter,optimized:this.isIOS(),icon:{url:"/img/mapmarkers/none-selected.png",size:[32,32],scaledSize:[22,22]},map:this.gmap};e.defaultCmarkerIcon&&delete l.icon;t=this.generalElementFromOption(l);new mapboxgl.Marker({element:t,anchor:"bottom"}).setLngLat(this.mapCenter).addTo(this.gmap)}var p=e.bndsChanged||function(){},m=e.dragStart||null,d=e.tilesLoaded||null;e.zoomChanged;return m&&this.gmap.on("dragend",m),d&&console.warn("Not implemented yet!"),this.gmap.on("zoomend",p),this.gmap.on("dragend",p),this.gmap.on("load",p),"1"===vars.gps?this.locateMe():this.mapbox._mapReady(a.center)},locateMe:function(e,t){this.mapbox.locateMe(e,t)},_showUmarker:function(e){this.mapbox._showUmarker(e)},saveLocation:function(){var e,t;if(this.gmap&&this.gmap.getCenter)return e=this.gmap.getCenter(),t=this.gmap.getZoom(),localStorage.lastMapLocZoom=e.lat+","+e.lng+","+t},resized:function(){this.gmap.resize()},getCenter:function(){return this.gmap.getBounds().getCenter()},recenter:function(e){this.fitBounds(e);this.gmap.getBounds().getCenter().lat;var t=this.gmap.getBounds().getCenter().long;this.gmap.setCenter=t},fitBounds:function(e,t){if(e&&0!=e.length){var r=new mapboxgl.LngLatBounds;if(e.length<2){var n=[(o=e[0]).lng-.002,o.lat+.002],a=[o.lng+.002,o.lat-.002];r.extend([n,a])}else for(var o of e)o.lat&&o.lng&&r.extend([o.lng,o.lat]);t&&r.extend([t.lng,t.lat]),this.gmap.fitBounds(r,{padding:80,duration:300})}},recenterWithZoom:function(e,t){t=t||10,(e||e.lat||e.lng)&&this.gmap&&(this.gmap.setZoom(t),this.gmap.setCenter([e.lng,e.lat]))},setMapTypeId:function(e){if(-1!=["HYBRID","TERRAIN","SATELLITE","ROADMAP"].indexOf(e)){var t="streets-v11";"HYBRID"==e?t="satellite-streets-v11":"TERRAIN"==e?t="light-v10":"SATELLITE"==e?t="satellite-v9":"ROADMAP"==e&&(t="streets-v11"),t="mapbox://styles/mapbox/"+t,this.gmap.setStyle(t)}},zoomIn:function(){this.gmap.zoomIn()},zoomOut:function(){this.gmap.zoomOut()},getBounds:function(){return this.gmap?(this.saveLocation(),this.gmap.getBounds()):null},getIcon:e.getIcon,getPriceImg:function(e,t,r){var n={url:"/img/mapmarkers/price.png",size:[56,31],origin:[-5,-5.5],anchor:"bottom",scaledSize:[45,25]};if(this.isIOS()||(n.origin=[-5,-5]),"function"==typeof t){var a,o,s=t(e,r);n.url=s.url,s.size&&(n.size=s.size),s.scaledSize&&(n.scaledSize=s.scaledSize),s.origin&&(n.origin=s.origin),(a=s.zIndex)&&(n.zIndex=a),(o=s.addedStyle)&&(n.addedStyle=o)}return n},createMarker:function(t,r,n,a,o){var s,i;n?(s=n(r.objs,null,e.vueSelf),i=n(r.objs,!0,e.vueSelf)):this.getIcon&&(s=this.getIcon(r.objs,null,e.vueSelf),i=this.getIcon(r.objs,!0,e.vueSelf));var l,p,m,d={mgName:t,position:[r.lng,r.lat],icon:s,map:this.gmap,optimized:this.isIOS()};r.draggable&&(d.draggable=!0),r.label&&(d.label=r.label),r.url&&(d.url=r.url),e.getLabelFunc&&"img"==a&&(l={text:e.getLabelFunc(r.objs,null,e.vueSelf),color:e.labelColor||"white",fontSize:"10px"},p=this.getPriceImg(r.objs,e.getImgFunc,!1),m=this.getPriceImg(r.objs,e.getImgFunc,!0),p.zIndex&&(d.zIndex=p.zIndex),d.label=l,d.icon=p),a&&(d.elementType=a),o&&(d.style=o),r.el=this.generalElementFromOption(d);let c={anchor:"bottom"};r.el&&(c.element=r.el),r.draggable&&(c.draggable=!0),r.mkr=new mapboxgl.Marker(c),r.mkr.setLngLat(d.position).addTo(this.gmap),r.mkr.setIcon=e=>{"object"==typeof e&&(e=e.url),r&&r.mkr&&("img"==a?r.mkr.getElement().setAttribute("src",e):r.mkr.getElement().style.backgroundImage=`url('${e}')`)},"img"==a&&(r.mkr.setLabel=e=>{"object"==typeof e&&(e=e.text),r&&r.mkr&&(r.mkr.getElement().innerHTML=e)}),r.mkr.iconNor=s,r.mkr.iconSel=i,l&&(r.mkr.rmLabel=l,r.mkr.rmIcon=p,r.mkr.rmIconSel=m);var u=e.dragMarkerStart||null,g=e.dragMarkerEnd||null;return u&&r.mkr.on("drag",(function(){u()})),g&&r.mkr.on("dragend",(function(){g(r.ids,r.mkr.getLngLat())})),r.draggable||r.el.addEventListener("click",(n=>{var a;if(null!=(a=e.vueSelf.mapObj.curSelMarker)&&(a.rmLabel?(a.setLabel(a.rmLabel),a.setIcon(a.rmIcon)):a.setIcon(e.vueSelf.mapObj.curSelMarker.iconNor)),r.mkr.rmLabel?r.mkr.setIcon(r.mkr.rmIconSel):r.mkr.setIcon(r.mkr.iconSel),e.vueSelf.mapObj.curSelMarker=r.mkr,e.sendMsg)return e.sendMsg(t+"MarkerClicked",r.ids),n.stopPropagation()}),!1),r.mkr},removeMarker:function(e){return e.mkr.remove(),delete e.mkr},triggerClick:function(e,t){var r=this.markerGroups[e];for(var n in r){var a=r[n];if(a.ids.indexOf(t)>-1)return void(a.el&&a.el.click())}},cluster_key:function(e){return this.round(e.lat)+","+this.round(e.lng)},round:function(e,t){return null==t&&(t=5),Math.round(e*Math.pow(10,t))/Math.pow(10,t)},setMarkers:function(t,r,n,a,o){var s,i,l,p,m,d,c,u,g,h=e.defaultIDName||"_id";for(null==this.markerGroups&&(this.markerGroups={}),m=this.markerGroups[t]||{},d={},s=0,p=r.length;s<p;s++)u=r[s],(c=d[l=this.cluster_key(u)]||{key:l,lat:u.lat,lng:u.lng,ids:[],objs:[],draggable:u.draggable,label:u.label,url:u.url}).ids.push(u[h]),c.objs.push(u),d[l]=c;for(i in d)(g=d[i]).ids.sort();for(i in m)g=m[i],null==d[i]||d[i].ids.toString()!==g.ids.toString()?(this.removeMarker(g),delete m[i]):delete d[i];for(i in d)g=d[i],this.createMarker(t,g,n,a,o),delete g.objs,m[i]=g;this.markerGroups[t]=m},getAllGroupMarkers:function(e){var t=this.markerGroups[e]||{},r=[];for(let e in t){let n=t[e];n.mkr&&r.push(n.mkr)}return r},clearMarkers:function(e,t={}){var r,n,a,o="default_skip";if(this.markerGroups&&(n=this.markerGroups[e])){for(r in t.skip&&t.skip.length&&(o=t.skip[0]+""),n)(a=n[r]).ids[0]+""!=o&&(this.removeMarker(a),delete n[r]);t.skip&&t.skip.length||delete this.markerGroups[e]}},createOrUpdateMarker:function(e,t,r){if(t.mkr)t.mkr.setLngLat([r[1],r[0]]);else{t.lat=r[0],t.lng=r[1],this.setMarkers(e,[t]);let n=this.cluster_key(t);t.mkr=this.markerGroups[e][n].mkr}},initAutocomplete:function(e,t){return this.mapbox.initAutocomplete(e,t)},displayRoute:function(){mapboxTransitService.route()},setCenter:function({lat:e,lng:t}){this.mapbox.setCenter([t,e])}};return e.canGeoCode&&(t=Object.assign(t,this.get_map_geo_fn())),t}}},previewInfo={props:{prop:{type:Object,default:{}}},data:()=>({}),computed:{soldOrLeased:function(){return"Sold"==this.prop.saleTpTag_en||["Sld","Lsd","Pnd","Cld"].includes(this.prop.lst)}},mounted(){},methods:{ImgUrl(){return"url("+this.prop.thumbUrl+'), url("/img/noPic.png")'},formatPrice:e=>"number"==typeof e?"$"+e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):e,openDetail(e){var t=/^RM/.test(e.id)?e.id:e._id;openPopup(`/1.5/prop/detail/inapp?id=${t}`,this.$_("RealMaster"))},closePreview(){bus.$emit("close-preview")},dotdate(e,t,r="."){if(!e)return"";"number"==typeof e&&(e=(e+="").slice(0,4)+"/"+e.slice(4,6)+"/"+e.slice(6,8)),"string"!=typeof e||/\d+Z/.test(e)||(e+=" EST");var n=t?"年":r,a=t?"月":r,o=t?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(e)&&!/\d+Z/.test(e)){var s=e.split(" ")[0].split("-");return s[0]+n+s[1]+a+s[2]+o}var i=new Date(e);return!i||isNaN(i.getTime())?e:i.getFullYear()+n+(i.getMonth()+1)+a+i.getDate()+o}},template:'\n  <div id="previewInfo" @click="openDetail(prop)">\n    <div class="viewImg" :style="{\'background-image\': ImgUrl()}"></div>\n    <div class="viewHead" @click.stop="closePreview()">\n      <div class="viewHasOh pull-left" v-if="prop.hasOh"><span class="fa fa-rmhistory timeIcon"></span>{{$_(\'Open House\')}}</div>\n      <div class="bgHead  pull-right"><span class="icon icon-close"></span></div>\n    </div>\n    <div class="bottomInfo">\n      <div class="propDisplay">\n        <div>\n          <span class="viewPrice" v-if="prop.sp && prop.priceValStrRed">{{formatPrice(prop.sp)}}</span>\n          <span class="viewPrice" v-show="prop.lp || prop.lpr" :class="{\'through\':soldOrLeased}">{{formatPrice(prop.lp || prop.lpr)}}</span>\n        </div>\n        <div class="viewBdrms">\n          <span class="viewRmbed" v-show="prop.bdrms">\n            <span class="fa fa-rmbed"></span>\n            <span class="viewBold">{{prop.bdrms}} {{prop.br_plus?\'+ \'+prop.br_plus:\'\'}}</span>\n          </span>\n          <span v-show="prop.rmbthrm || prop.tbthrms || prop.bthrms">\n            <span class="fa fa-rmbath"></span>\n            <span class="viewBold">{{prop.rmbthrm || prop.tbthrms|| prop.bthrms}}</span>\n          </span>\n          <span v-show="prop.rmgr || prop.tgr || prop.gr">\n            <span class="fa fa-rmcar"></span>\n            <span class="viewBold">{{prop.rmgr || prop.tgr || prop.gr}}</span>\n          </span>\n        </div>\n      </div>\n      <div class="propDisplay">\n        <div>\n          <span class="viewTypeTag" v-if="prop.saleTpTag">{{prop.saleTpTag}}</span>\n          <span v-if="prop.spcts||prop.mt||prop.ts">&nbsp;({{dotdate(prop.spcts||prop.mt||prop.ts)}})</span>\n          <span v-show="prop.ltp == \'exlisting\'">{{$_(\'Exclusive\')}}</span>\n          <span v-show="prop.ltp == \'assignment\'">{{$_(\'Assignment\')}}</span>\n          <span v-show="prop.ltp == \'rent\' && !prop.cmstn">{{$_(\'Landlord Rental\')}}</span>\n          <span v-show="prop.ltp == \'rent\' && prop.cmstn">{{$_(\'Exclusive Rental\')}}</span></span>\n          <span class="viewDom" v-show="prop.dom != null"> &#183; {{$_(\'DOM\',\'prop\')}} {{prop.dom}}</span>\n        </div>\n      </div>\n      <div class="propDisplay">\n        <div class="viewTrim">\n          <span class="addr" v-show="prop.addr">{{prop.unt?prop.unt:\'\'}} {{prop.addr}} {{prop.apt_num}}</span>\n          <span class="viewPtype"> &#183; {{prop.ptype2?prop.ptype2.join(\' \'):\'\'}}</span>\n        </div>\n        <div class="viewSid pull-right" v-if="prop.sid">{{prop.sid}}</div>\n      </div>\n    </div>\n  </div>\n  '};function time(e){return(e=new Date(e)).getMonth()+1+"/"+e.getDate()+" "+e.getHours()+":"+(e.getMinutes()<10?"0":"")+e.getMinutes()}function day(e){if(e)return(e=new Date(e)).getUTCDate()}function yearMonth(e){if(e)return(e=new Date(e)).getFullYear()+"."+(e.getUTCMonth()+1)}function number(e,t){return null!=e?(t=parseInt(t),isNaN(e)?0:parseFloat(e.toFixed(t))):e}function propPrice(e,t){return null!=e?(e=parseInt(e),isNaN(e)||0==e?"":(e<1e3?e+="":e=e<1e4?(e/1e3).toFixed(1)+"K":e<999500?Math.round(e/1e3).toFixed(0)+"K":(e/1e6).toFixed(t=t||1)+"M",e)):""}function percentage(e,t){return null!=e?(e=parseFloat(e),isNaN(e)?0:(100*e).toFixed(2)):e}function dotdate(e,t,r="."){if(!e)return"";"number"==typeof e&&(e=(e+="").slice(0,4)+"/"+e.slice(4,6)+"/"+e.slice(6,8)),"string"!=typeof e||/\d+Z/.test(e)||(e+=" EST");var n=t?"年":r,a=t?"月":r,o=t?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(e)&&!/\d+Z/.test(e)){var s=e.split(" ")[0].split("-");return s[0]+n+s[1]+a+s[2]+o}var i=new Date(e);return!i||isNaN(i.getTime())?e:i.getFullYear()+n+(i.getMonth()+1)+a+i.getDate()+o}function datetime(e){return(e=new Date(e)).getMonth()+1+"/"+e.getDate()+"/"+e.getFullYear()+" "+e.getHours()+":"+(e.getMinutes()<10?"0":"")+e.getMinutes()}function monthNameAndDate(e){if(!e)return"";var t=new Date(e);return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sept","Oct","Nov","Dec"][t.getMonth()]+"."+t.getDate()}function currency(e,t="$",r){try{if("string"==typeof e&&-1!=e.indexOf(t))return e;var n=parseInt(e);if(isNaN(n))return null;n<100&&r<2&&(r=2);var a=e.toString().split(".");return a[0]=a[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),0==r?a[1]=void 0:r>0&&a[1]&&(a[1]=a[1].substr(0,r)),t+a.filter((function(e){return e})).join(".")}catch(e){return console.error(e),null}}function arrayValue(e){return Array.isArray(e)?e.join(" "):e}var filters={time:time,day:day,number:number,dotdate:dotdate,datetime:datetime,propPrice:propPrice,percentage:percentage,yearMonth:yearMonth,monthNameAndDate:monthNameAndDate,currency:currency,arrayValue:arrayValue},notesMap={data:()=>({mapObj:null,notes:[],curProps:[],noteMarkerGroupName:"notes",showPreview:!1,curMarkId:"",mapDispMode:"ROADMAP",cMarker:0}),mounted(){let e=this;if(e.$getTranslate(e),window.bus){var t=window.bus;e.initGmap(),t.$on(e.noteMarkerGroupName+"MarkerClicked",(t=>{e.markerClick(t)})),t.$on("close-preview",(function(t){e.showPreview=!1}))}else console.error("global bus is required!")},methods:{markerClick(e){var t=this,r=e[0];if(t.showPreview&&0!=t.curMarkId.length&&t.curMarkId==r)return void(t.showPreview=!1);let n=t.notes.findIndex((e=>e._id==r));t.curProps=t.notes[n].props,t.curMarkId=r,t.showPreview=!0},getIconChar(e){if(e.isProj){if(e.saletp_en=["Sale"],e.ptype2_en=[e.tp1||"",e.tp2||""],!e.tp1&&!e.tp2)return"P";/Office|Retail/.test(e.tp1)&&(e.pclass=["b"])}var t=e.saletp_en||e.saletp||"",r=e.ptype2_en||e.ptype2;if(t.indexOf("Sale")>-1){var n="none";r=r.join(",");/Semi-/.test(r)?n="S":/Detached|Det\sComm/.test(r)?n="D":/Apt|Apartment|Condo/.test(r)?n="C":/Att|Townhouse/.test(r)?n="T":"Commercial"==e.ptype&&(n="B")}else n="L";return n},getLabelFunc(e){if(e.length&&e.length>1)return e.length+"";var t=this.getIconChar(e[0]);"none"==t&&(t=""),t&&(t=this.$_(t,"map label char"));var r=e[0];return r.isProj&&(r.lpr=r.lpf),t+filters.propPrice(r.lp||r.lpr)},getNoteIcon(e,t){let r="/img/mapmarkers",n={url:`${r}/none-default.png`,scaledSize:[24,24],addedStyle:"display: flex;align-items: center;justify-content: center;"};return!t&&e&&e[0].props.length>1?n:t&&e&&e[0].props.length>1?(n.url=`${r}/none-sel.png`,n):(delete n.scaledSize,delete n.addedStyle,!t&&e&&1==e[0].props.length?("Delisted"==e[0].props[0].saleTpTag_en?n.url=`${r}/off-price.png`:n.url=`${r}/price.png`,n):t&&e&&1==e[0].props.length?(n.url=`${r}/sel-price.png`,n):void 0)},zoomIn(){this.mapObj.zoomIn()},zoomOut(){this.mapObj.zoomOut()},locateMe(){var e=this;e.mapObj.locateMe(null,(t=>{if(!e.cMarker){var r=new mapboxgl.LngLat(t[1],t[0]),n=document.createElement("img");n.setAttribute("src","/img/mapmarkers/umarker.png"),n.setAttribute("style","width:25px;height:25px;z-index:2"),e.cMarker=new mapboxgl.Marker({element:n,anchor:"bottom"}).setLngLat(r).addTo(e.mapObj.gmap)}}))},setMapTypeId(){var e=this;if(e.mapObj)return"ROADMAP"==e.mapDispMode?e.mapDispMode="HYBRID":e.mapDispMode="ROADMAP",e.mapObj.setMapTypeId(e.mapDispMode)},addLatLngToNotes(e=[]){let t=[];for(let r=0,n=e.length;r<n;r++){let n={props:[]},a=t.findIndex((t=>t.lat.toString()==e[r].lat.toString()&&t.lng.toString()==e[r].lng.toString()));if(-1==a)n._id=e[r]._id,n.props.push(e[r]),n.lat=e[r].lat,n.lng=e[r].lng,n.label={text:this.getLabelFunc(n.props),color:"white",fontSize:"10px"},t.push(n);else{-1==t[a].props.findIndex((t=>t._id==e[r]._id))&&(t[a].props.push(e[r]),t[a].label={text:this.getLabelFunc(t[a].props),color:"white",fontSize:"10px"})}}return t},getNotesData(){let e=this;fetchData("/1.5/notes/savedList",{body:{model:"map"}},((t,r)=>{if(t||!r.ok){let n=t?e.$_("Error"):e.$_(r.e);return bus.$emit("flash-message",n)}this.notes=this.addLatLngToNotes(r.result.propList),e.setMarkersPot()}))},setMarkersPot(){let e=this;e.mapObj.fitBounds(e.notes),e.mapObj.setMarkers(e.noteMarkerGroupName,e.notes,e.getNoteIcon,null,null)},initGmap(){var e,t=this,r=window.bus;e=[],t.sendMsg=(n,a)=>{if(!r)return t?void t.$emit(n,a):e.push({e:n,m:a});r.$emit(n,a)};var n={bndsChanged:t.bndsChanged,mapTypeControl:!0,sendMsg:t.sendMsg,defaultIDName:"_id",vueSelf:t,hfHeight:44,defaultCmarkerIcon:!0};t.mapObj=t.getMapObject(n),t.mapObj.init("id_d_map"),setTimeout((()=>{"claim"==vars.from?t.getClaimedProps():t.getNotesData()}),500)},goBackNotes(){window.rmCall(":ctx::cancel")},getClaimedProps(){var e=this;e.loading=!0;const t={};["agent","date","side","sort","tp"].forEach((e=>{vars[e]&&(t[e]=vars[e])})),fetchData("/propClaim/map",{body:t},(function(t,r){if(e.loading=!1,t||!r.ok){let n=t?e.$_("Error"):e.$_(r.e);return bus.$emit("flash-message",n)}e.notes=e.addLatLngToNotes(r.items||[]),e.setMarkersPot()}))}}};initUrlVars();var app=Vue.createApp(notesMap);trans.install(app,{ref:Vue.ref}),app.mixin(map_mixins),app.component("preview-info",previewInfo),app.component("flash-message",flashMessage),app.mount("#notesMap");
