

{{&
  var {css,js,tabs,id,d} = it;
}}
{{~ css :c:idx}}
<link rel="stylesheet" href="{{= c}}">
{{~}}
<div id="forumHome" style='height: 100vh;overflow: hidden;' v-cloak>
  <div id="forum-main" class="container">
    <div id="forum-containter" v-on:scroll="listScrolled" class="row">
        <div v-for="post in allPosts" track-by="$index" :key="post._id" class="col-xs-12 col-sm-6 col-md-4">
          <forum-summary-card parent-page="forumList" :post="post" :disp-var="dispVar" :display-page="displayPage" :no-tag="noTag" cur-style="up-down"></forum-summary-card>
        </div>

        <div style="padding-bottom: 20px">
            <div class="pull-spinner" v-if="loading" style="display:block"></div>
        </div>
    </div>
  </div>
</div>

{{~ js :j:idx}}
<script type="text/javascript" src="{{= j}}"></script>
{{~}}